{"name": "menumaker_admin", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "populate-ingredients": "npx tsx scripts/populate_ingredients.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query": "^5.81.0", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "fuse.js": "^7.1.0", "lucide-react": "^0.522.0", "next": "^15.3.4", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}, "devDependencies": {"tw-animate-css": "^1.3.4"}}
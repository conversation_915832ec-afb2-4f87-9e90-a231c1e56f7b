# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the menumaker_admin project - a simple admin interface for managing a meal planner app's content. The admin portal connects directly to a Supabase backend to enable easy management of recipes, meals, and user analytics.

## Tech Stack

- **Frontend**: Next.js 14+ with App Router and TypeScript
- **UI Framework**: Shadcn/ui + Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **State Management**: TanStack Query (React Query)
- **Styling**: Tailwind CSS

## Development Setup

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Architecture

```
/app
  /meals         - Main meal/recipe management interface
  /analytics     - Metrics and reporting
  /api           - API routes for Supabase operations
/components      - Reusable UI components
/lib             - Supabase client and utilities
```

## Key Features

- **Meal Management**: CRUD operations for recipes and meals
- **Ingredient Management**: Manage recipe ingredients and reviews
- **Recipe Scraping**: Import recipes from external websites
- **Image Upload**: Upload and manage meal images via Supabase storage
- **Authentication**: Simple admin access with Supabase auth

## Common Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

-- Script to remove master_ingredient_id from meals ingredients JSON
-- This script will update all meals that have ingredients with master_ingredient_id
-- and remove that field while preserving all other ingredient data

-- First, let's check how many meals have master_ingredient_id
SELECT COUNT(DISTINCT m.id) as affected_meals_count
FROM meals m
WHERE m.ingredients::text LIKE '%master_ingredient_id%';

-- Update all meals to remove master_ingredient_id from ingredients
UPDATE meals
SET ingredients = (
    SELECT jsonb_agg(
        CASE 
            WHEN jsonb_typeof(ingredient) = 'object' THEN
                ingredient - 'master_ingredient_id'
            ELSE
                ingredient
        END
    )
    FROM jsonb_array_elements(ingredients) AS ingredient
)
WHERE ingredients IS NOT NULL
  AND ingredients::text LIKE '%master_ingredient_id%';

-- Verify the update by checking again
SELECT COUNT(DISTINCT m.id) as remaining_meals_with_master_ingredient_id
FROM meals m
WHERE m.ingredients::text LIKE '%master_ingredient_id%';

-- Optional: Show a sample of meals to verify the structure
SELECT id, name, ingredients
FROM meals
WHERE ingredients IS NOT NULL
LIMIT 5;
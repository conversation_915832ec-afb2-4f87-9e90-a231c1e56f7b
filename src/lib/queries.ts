'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  supabase,
  type Meal,
  type UserMeal,
  type AIGeneratedMeal,
  checkSupabaseConnection,
} from './supabase';

// Retry configuration for better reliability
const queryConfig = {
  retry: 3,
  retryDelay: (attemptIndex: number) =>
    Math.min(1000 * 2 ** attemptIndex, 30000),
  staleTime: 30 * 1000, // 30 seconds (reduced from 5 minutes)
  gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime)
};

// Meal queries
export const useMeals = () => {
  return useQuery({
    queryKey: ['meals'],
    queryFn: async () => {
      // Check connection health first
      const isHealthy = await checkSupabaseConnection();
      if (!isHealthy) {
        throw new Error('Supabase connection unhealthy');
      }

      const { data, error } = await supabase
        .from('meals')
        .select('*')
        .order('createdAt', { ascending: false })
        .range(0, 99999); // Explicitly specify very large range to get all meals

      if (error) {
        console.error('Meals query error:', error);
        throw error;
      }
      return data as Meal[];
    },
    ...queryConfig,
  });
};

// Paginated meals query for admin interface
export const usePaginatedMeals = (
  page: number = 1,
  pageSize: number = 50,
  sortBy: string = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc',
  searchTerm: string = ''
) => {
  return useQuery({
    queryKey: [
      'meals',
      'paginated',
      page,
      pageSize,
      sortBy,
      sortOrder,
      searchTerm,
    ],
    queryFn: async () => {
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;

      let query = supabase.from('meals').select('*', { count: 'exact' });

      // Add search filters if search term exists - prioritize name field
      if (searchTerm.trim()) {
        // Primary search on name field, with secondary matches on other fields
        query = query.or(
          `name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,course.ilike.%${searchTerm}%`
        );
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      const { data, error, count } = await query.range(from, to);

      if (error) throw error;

      return {
        data: data as Meal[],
        count: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
        currentPage: page,
        pageSize,
        sortBy,
        sortOrder,
        searchTerm,
      };
    },
  });
};

export const useMeal = (id: string) => {
  return useQuery({
    queryKey: ['meal', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('meals')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Meal;
    },
  });
};

// User meals queries
export const useUserMeals = () => {
  return useQuery({
    queryKey: ['userMeals'],
    queryFn: async () => {
      // user_meals table removed - return empty array
      return [];
    },
  });
};

export const useUserMeal = (id: string) => {
  return useQuery({
    queryKey: ['userMeal', id],
    queryFn: async () => {
      // user_meals table removed - return null
      return null;
    },
  });
};

// AI generated meals queries
export const useAIGeneratedMeals = () => {
  return useQuery({
    queryKey: ['aiGeneratedMeals'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('ai_generated_meals')
        .select('*')
        .order('generated_at', { ascending: false });
      // No limit - Pro plan allows unlimited rows

      if (error) throw error;
      return data as AIGeneratedMeal[];
    },
  });
};

// Mutations
export const useCreateMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      meal: Omit<Meal, 'id' | 'created_at' | 'updated_at'>
    ) => {
      const { data, error } = await supabase
        .from('meals')
        .insert([meal])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meals'] });
    },
  });
};

export const useUpdateMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Meal> & { id: string }) => {
      console.log('Updating meal:', id);

      // Use the API route instead of direct Supabase call
      const response = await fetch(`/api/meals/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Update failed');
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['meals'] });
      queryClient.invalidateQueries({ queryKey: ['meal', data.id] });
    },
  });
};

export const useDeleteMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting meal:', id);

      // Use the API route instead of direct Supabase call
      const response = await fetch(`/api/meals/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Delete failed');
      }

      const result = await response.json();
      console.log('Delete result:', result);
      return result;
    },
    onSuccess: () => {
      // Invalidate all meal-related queries to ensure UI updates
      queryClient.invalidateQueries({ queryKey: ['meals'] });
    },
  });
};

// User meal mutations
export const useCreateUserMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      meal: any
    ) => {
      // user_meals table removed - return null
      return null;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userMeals'] });
    },
  });
};

export const useUpdateUserMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      ...updates
    }: any) => {
      // user_meals table removed - return null
      return null;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['userMeals'] });
    },
  });
};

export const useDeleteUserMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      // user_meals table removed - do nothing
      return;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userMeals'] });
    },
  });
};

// AI Generated Meal mutations
export const useUpdateAIGeneratedMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      ...updates
    }: Partial<AIGeneratedMeal> & { id: string }) => {
      const { data, error } = await supabase
        .from('ai_generated_meals')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiGeneratedMeals'] });
    },
  });
};

export const useApproveAIGeneratedMeal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await supabase
        .from('ai_generated_meals')
        .update({ approved_for_main_db: true, reviewed: true })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiGeneratedMeals'] });
    },
  });
};

// Dashboard analytics queries
export const useDashboardAnalytics = () => {
  return useQuery({
    queryKey: ['dashboardAnalytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_dashboard_analytics');

      if (error) {
        // Fallback to individual queries if RPC doesn't exist
        const [
          { data: mealsCount },
          { data: usersCount },
          { data: aiMealsCount },
          { data: recentMeals },
          { data: cuisineStats },
          { data: nutritionStats },
        ] = await Promise.all([
          supabase.from('meals').select('*', { count: 'exact', head: true }),
          supabase
            .from('user_preferences')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('ai_generated_meals')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('meals')
            .select('*')
            .order('createdAt', { ascending: false })
            .limit(5),
          supabase
            .from('meals')
            .select('cuisine_type')
            .not('cuisine_type', 'is', null),
          supabase
            .from('meals')
            .select(
              'calories, protein, carbs, fats, prepTimeMin, prepTimeHour, cookTimeMin, cookTimeHour, estimated_cost_per_serving'
            )
            .not('calories', 'is', null),
        ]);

        return {
          totalMeals: mealsCount || 0,
          totalUsers: usersCount || 0,
          totalAIMeals: aiMealsCount || 0,
          recentMeals: recentMeals || [],
          cuisineStats: cuisineStats || [],
          nutritionStats: nutritionStats || [],
        };
      }

      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// User engagement metrics
export const useUserEngagement = () => {
  return useQuery({
    queryKey: ['userEngagement'],
    queryFn: async () => {
      try {
        console.log('Fetching user engagement metrics...');

        const [
          totalUsersResult,
          activeSubscribersResult,
          premiumUsersResult,
          totalMealPlansResult,
          totalFavoritesResult,
        ] = await Promise.all([
          supabase
            .from('user_preferences')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('user_preferences')
            .select('*', { count: 'exact', head: true })
            .eq('subscription_status', 'active'),
          supabase
            .from('user_preferences')
            .select('*', { count: 'exact', head: true })
            .eq('subscription_tier', 'premium'),
          supabase
            .from('weekly_meal_plans')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('user_favorites')
            .select('*', { count: 'exact', head: true }),
        ]);

        console.log('Raw results:', {
          totalUsers: totalUsersResult,
          activeSubscribers: activeSubscribersResult,
          premiumUsers: premiumUsersResult,
          totalMealPlans: totalMealPlansResult,
          totalFavorites: totalFavoritesResult,
        });

        // Check for any errors
        const errors = [
          totalUsersResult.error,
          activeSubscribersResult.error,
          premiumUsersResult.error,
          totalMealPlansResult.error,
          totalFavoritesResult.error,
        ].filter(Boolean);
        if (errors.length > 0) {
          console.error('User engagement query errors:', errors);
          throw new Error('Failed to fetch user engagement metrics');
        }

        const result = {
          totalUsers: totalUsersResult.count || 0,
          activeSubscribers: activeSubscribersResult.count || 0,
          premiumUsers: premiumUsersResult.count || 0,
          totalMealPlans: totalMealPlansResult.count || 0,
          totalFavorites: totalFavoritesResult.count || 0,
        };

        console.log('Processed user engagement result:', result);
        return result;
      } catch (error) {
        console.error('User engagement fetch failed:', error);
        throw error;
      }
    },
    ...queryConfig,
  });
};

// AI content metrics
export const useAIContentMetrics = () => {
  return useQuery({
    queryKey: ['aiContentMetrics'],
    queryFn: async () => {
      const [
        { count: pendingReviews },
        { count: approvedMeals },
        { count: recentAIGenerations },
      ] = await Promise.all([
        supabase
          .from('ai_generated_meals')
          .select('*', { count: 'exact', head: true })
          .eq('reviewed', false),
        supabase
          .from('ai_generated_meals')
          .select('*', { count: 'exact', head: true })
          .eq('approved_for_main_db', true),
        supabase
          .from('ai_generated_meals')
          .select('*', { count: 'exact', head: true })
          .gte(
            'generated_at',
            new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          ),
      ]);

      return {
        pendingReviews: pendingReviews || 0,
        approvedMeals: approvedMeals || 0,
        recentGenerations: recentAIGenerations || 0,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for AI metrics
  });
};

// User management queries
export const useUsers = () => {
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      // Get users from auth.users and supplement with user_preferences data
      const { data: authUsers, error: authError } =
        await supabase.auth.admin.listUsers();

      if (authError) {
        console.error('Users query error:', authError);
        throw authError;
      }

      // Get user preferences to supplement auth data
      const { data: preferences, error: prefError } = await supabase
        .from('user_preferences')
        .select(
          'user_id, created_at, household_size, cooking_skill, max_cook_time'
        );

      if (prefError) {
        console.error('User preferences query error:', prefError);
        // Continue without preferences data
      }

      // Combine auth data with preferences
      const users =
        authUsers?.users?.map((user) => {
          const userPrefs = preferences?.find((p) => p.user_id === user.id);
          return {
            id: user.id,
            email: user.email,
            created_at: user.created_at,
            user_role: user.app_metadata?.user_role || 'user',
            subscription_status: 'unknown', // Not tracked in current schema
            household_size: userPrefs?.household_size,
            cooking_skill: userPrefs?.cooking_skill,
            max_cook_time: userPrefs?.max_cook_time,
          };
        }) || [];

      return users.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    },
    ...queryConfig,
  });
};

export const useUpdateUserSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      tier,
      status,
    }: {
      userId: string;
      tier?: string;
      status?: string;
    }) => {
      const updates: any = {};
      if (tier) updates.subscription_tier = tier;
      if (status) updates.subscription_status = status;

      const { data, error } = await supabase
        .from('user_preferences')
        .update(updates)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['userEngagement'] });
    },
  });
};

export const useUpdateUserLimits = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      dailyLimit,
    }: {
      userId: string;
      dailyLimit: number;
    }) => {
      const { data, error } = await supabase
        .from('user_preferences')
        .update({ daily_messages_limit: dailyLimit })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
};

// New dashboard queries for updated metrics
export const useUserMetrics = () => {
  return useQuery({
    queryKey: ['userMetrics'],
    queryFn: async () => {
      // Get basic user count from auth.users
      const { data: users, error: usersError } =
        await supabase.auth.admin.listUsers();

      if (usersError) {
        console.error('Error fetching users:', usersError);
        // Fallback: try to get user count from user_preferences as proxy
        const { count, error: countError } = await supabase
          .from('user_preferences')
          .select('*', { count: 'exact', head: true });

        if (countError) throw countError;

        return {
          totalUsers: count || 0,
          activeSubscribers: 0, // Can't determine without subscription data
          totalMessages: 0, // Not available without message tracking
          avgMessagesPerUser: 0,
          estimatedTotalCost: 0,
          avgCostPerUser: 0,
        };
      }

      const totalUsers = users?.users?.length || 0;

      // For now, return basic metrics since we don't have subscription/message data
      // These could be enhanced later with actual subscription tracking
      return {
        totalUsers,
        activeSubscribers: Math.floor(totalUsers * 0.3), // Estimate 30% active
        totalMessages: 0, // Not tracked yet
        avgMessagesPerUser: 0,
        estimatedTotalCost: 0,
        avgCostPerUser: 0,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useMealCompleteness = () => {
  return useQuery({
    queryKey: ['mealCompleteness'],
    queryFn: async () => {
      // Paginate through all meals to get complete dataset
      let allMeals: any[] = [];
      let currentPage = 0;
      const pageSize = 1000;
      let hasMore = true;

      while (hasMore) {
        const from = currentPage * pageSize;
        const to = from + pageSize - 1;

        const { data, error } = await supabase
          .from('meals')
          .select(
            `
            name,
            description,
            instructions,
            ingredients,
            calories,
            course,
            cuisine_type
          `
          )
          .range(from, to);

        if (error) throw error;

        if (data && data.length > 0) {
          allMeals = [...allMeals, ...data];
          hasMore = data.length === pageSize; // Continue if we got a full page
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      console.log(
        'Meal completeness query returned:',
        allMeals.length,
        'meals'
      ); // Debug log

      const totalMeals = allMeals.length;
      const completeMeals = allMeals.filter(
        (meal) =>
          meal.name &&
          meal.description &&
          meal.instructions &&
          meal.ingredients &&
          meal.calories &&
          meal.calories > 0 &&
          meal.course &&
          meal.cuisine_type
      ).length;

      const completionPercentage =
        totalMeals > 0 ? (completeMeals / totalMeals) * 100 : 0;

      return {
        totalMeals,
        completeMeals,
        incompleteMeals: totalMeals - completeMeals,
        completionPercentage,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useTopFavoritedMeals = () => {
  return useQuery({
    queryKey: ['topFavoritedMeals'],
    queryFn: async () => {
      // Paginate through all meals to get complete dataset
      let allMeals: any[] = [];
      let currentPage = 0;
      const pageSize = 1000;
      let hasMore = true;

      while (hasMore) {
        const from = currentPage * pageSize;
        const to = from + pageSize - 1;

        const { data, error } = await supabase
          .from('meals')
          .select('id, name, course, cuisine_type, calories, author')
          .range(from, to);

        if (error) throw error;

        if (data && data.length > 0) {
          allMeals = [...allMeals, ...data];
          hasMore = data.length === pageSize;
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      const { data: favoritesData, error: favoritesError } = await supabase
        .from('user_favorites')
        .select('meal_id');

      if (favoritesError) throw favoritesError;

      // Count favorites per meal
      const favoriteCounts =
        favoritesData?.reduce(
          (acc, fav) => {
            acc[fav.meal_id] = (acc[fav.meal_id] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ) || {};

      // Add favorite counts to meals and sort, then take top 10 for display
      const mealsWithFavorites = allMeals
        .map((meal) => ({
          ...meal,
          favorite_count: favoriteCounts[meal.id] || 0,
        }))
        .sort((a, b) => b.favorite_count - a.favorite_count)
        .slice(0, 10);

      return mealsWithFavorites;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCuisineDistribution = () => {
  return useQuery({
    queryKey: ['cuisineDistribution'],
    queryFn: async () => {
      // Paginate through all meals to get complete dataset
      let allMeals: any[] = [];
      let currentPage = 0;
      const pageSize = 1000;
      let hasMore = true;

      while (hasMore) {
        const from = currentPage * pageSize;
        const to = from + pageSize - 1;

        const { data, error } = await supabase
          .from('meals')
          .select('cuisine_type')
          .not('cuisine_type', 'is', null)
          .range(from, to);

        if (error) throw error;

        if (data && data.length > 0) {
          allMeals = [...allMeals, ...data];
          hasMore = data.length === pageSize;
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      // Count cuisine types
      const cuisineCounts = allMeals.reduce(
        (acc, meal) => {
          const cuisine = meal.cuisine_type || 'Other';
          acc[cuisine] = (acc[cuisine] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      // Convert to chart data format
      const chartData = Object.entries(cuisineCounts)
        .sort(([, a], [, b]) => (b as number) - (a as number))
        .map(([cuisine, count]) => ({
          cuisine,
          count,
          percentage: allMeals.length
            ? ((count as number) / allMeals.length) * 100
            : 0,
        }));

      return chartData;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useContentStatus = () => {
  return useQuery({
    queryKey: ['contentStatus'],
    queryFn: async () => {
      // Paginate through all meals to get complete dataset
      let allMeals: any[] = [];
      let currentPage = 0;
      const pageSize = 1000;
      let hasMore = true;

      while (hasMore) {
        const from = currentPage * pageSize;
        const to = from + pageSize - 1;

        const { data, error } = await supabase
          .from('meals')
          .select('status')
          .range(from, to);

        if (error) throw error;

        if (data && data.length > 0) {
          allMeals = [...allMeals, ...data];
          hasMore = data.length === pageSize;
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      // Count by status
      const statusCounts = allMeals.reduce(
        (acc, meal) => {
          const status = meal.status || 'unknown';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      // Convert to chart data format
      const chartData = Object.entries(statusCounts).map(([status, count]) => ({
        status,
        count,
        percentage: allMeals.length
          ? ((count as number) / allMeals.length) * 100
          : 0,
      }));

      return chartData;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useRecentActivity = () => {
  return useQuery({
    queryKey: ['recentActivity'],
    queryFn: async () => {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Get recent meals
      const { data: recentMeals, error: mealsError } = await supabase
        .from('meals')
        .select('created_at, createdAt')
        .gte('created_at', thirtyDaysAgo.toISOString());

      if (
        mealsError &&
        !mealsError.message.includes('column "created_at" does not exist')
      )
        throw mealsError;

      // If created_at doesn't exist, try createdAt
      let recentMealsCount = 0;
      if (
        mealsError &&
        mealsError.message.includes('column "created_at" does not exist')
      ) {
        const { data: altRecentMeals, error: altError } = await supabase
          .from('meals')
          .select('createdAt')
          .gte('createdAt', thirtyDaysAgo.toISOString());

        if (!altError) {
          recentMealsCount = altRecentMeals?.length || 0;
        }
      } else {
        recentMealsCount = recentMeals?.length || 0;
      }

      // Get recent users
      const { data: recentUsers, error: usersError } = await supabase
        .from('user_preferences')
        .select('created_at')
        .gte('created_at', thirtyDaysAgo.toISOString());

      const recentUsersCount = usersError ? 0 : recentUsers?.length || 0;

      // Get recent AI generations
      const { data: recentAI, error: aiError } = await supabase
        .from('ai_generated_meals')
        .select('generated_at')
        .gte('generated_at', thirtyDaysAgo.toISOString());

      const recentAICount = aiError ? 0 : recentAI?.length || 0;

      return {
        recentMeals: recentMealsCount,
        recentUsers: recentUsersCount,
        recentAIGenerations: recentAICount,
        totalActivity: recentMealsCount + recentUsersCount + recentAICount,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

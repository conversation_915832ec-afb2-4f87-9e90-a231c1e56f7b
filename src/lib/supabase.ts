import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true, // Enable session persistence for auth
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'x-my-custom-header': 'menumaker-admin',
    },
  },
  realtime: {
    timeout: 10000,
    heartbeatIntervalMs: 30000,
  },
});

// Add connection health check
export const checkSupabaseConnection = async () => {
  try {
    const { error } = await supabase
      .from('meals')
      .select('id')
      .limit(1)
      .single();

    if (error && !error.message.includes('No rows')) {
      console.error('Supabase connection error:', error);
      return false;
    }
    return true;
  } catch (err) {
    console.error('Supabase health check failed:', err);
    return false;
  }
};

// Types for your database tables
export interface Meal {
  id: string;
  name: string;
  description?: string;
  image?: string;
  prepTimeMin?: number;
  prepTimeHour?: number;
  cookTimeMin?: number;
  cookTimeHour?: number;
  servingSize?: number;
  instructions?: any; // JSONB
  ingredients?: any; // JSONB
  ingredient_sections?: any; // JSONB - Grouped ingredients structure
  calories?: number;
  protein?: number;
  carbs?: number;
  fats?: number;
  order?: number;
  lastUsedDate?: string;
  createdAt?: string;
  updatedAt?: string;
  lastSelected?: string;
  createdById?: string;
  isUserRecipe?: boolean;
  course?: string;
  prep_method?: string;
  cuisine_type?: string;
  source_url?: string;
  author?: string;
  dietary_tags?: string[];
  allergen_contains?: string[];
  protein_type?: string;
  spice_level?: string;
  required_equipment?: string[];
  estimated_cost_per_serving?: number;
  meal_tags?: string[];
  status?: string;
}

export interface UserMeal {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  image?: string;
  prep_time_min?: number;
  prep_time_hour?: number;
  cook_time_min?: number;
  cook_time_hour?: number;
  serving_size?: number;
  instructions?: any; // JSONB
  ingredients?: any; // JSONB
  course?: string;
  cuisine_type?: string;
  spice_level?: string;
  dietary_tags?: string[];
  allergen_contains?: string[];
  created_at?: string;
  updated_at?: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fats?: number;
}

export interface AIGeneratedMeal {
  id: string;
  name: string;
  prep_time?: string;
  cook_time?: string;
  course?: string;
  cuisine_type?: string;
  user_query?: string;
  user_id?: string;
  generated_at?: string;
  times_generated?: number;
  last_generated_at?: string;
  estimated_prep_time_min?: number;
  estimated_cook_time_min?: number;
  estimated_difficulty?: string;
  dietary_tags?: string[];
  allergen_contains?: string[];
  spice_level?: string;
  reviewed?: boolean;
  approved_for_main_db?: boolean;
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  ingredients: any; // JSONB
  instructions: any; // JSONB
}

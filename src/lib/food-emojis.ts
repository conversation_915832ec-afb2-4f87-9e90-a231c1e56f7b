// Global food emoji definitions
export interface FoodEmoji {
  emoji: string;
  name: string;
  category: string;
}

// Create a deduplicated list of emojis
const createUniqueEmojis = (): FoodEmoji[] => {
  const emojiMap = new Map<string, FoodEmoji>();
  
  const allEmojis: FoodEmoji[] = [
  // Vegetables
  { emoji: '🥗', name: 'Salad', category: 'Vegetables' },
  { emoji: '🍅', name: '<PERSON><PERSON>', category: 'Vegetables' },
  { emoji: '🥕', name: 'Carrot', category: 'Vegetables' },
  { emoji: '🌽', name: 'Corn', category: 'Vegetables' },
  { emoji: '🥒', name: 'C<PERSON><PERSON><PERSON>', category: 'Vegetables' },
  { emoji: '🥬', name: 'Leafy Greens', category: 'Vegetables' },
  { emoji: '🥔', name: 'Potato', category: 'Vegetables' },
  { emoji: '🍠', name: 'Sweet Potato', category: 'Vegetables' },
  { emoji: '🧄', name: 'Garlic', category: 'Vegetables' },
  { emoji: '🧅', name: 'Onion', category: 'Vegetables' },
  { emoji: '🫒', name: 'Olive', category: 'Vegetables' },
  { emoji: '🌶️', name: 'Hot Pepper', category: 'Vegetables' },
  { emoji: '🫑', name: 'Bell Pepper', category: 'Vegetables' },
  { emoji: '🥑', name: 'Avocado', category: 'Vegetables' },
  { emoji: '🍆', name: 'Eggplant', category: 'Vegetables' },
  { emoji: '🥦', name: 'Broccoli', category: 'Vegetables' },
  { emoji: '🫛', name: 'Pea Pod', category: 'Vegetables' },
  { emoji: '🍄', name: 'Mushroom', category: 'Vegetables' },
  { emoji: '🥥', name: 'Coconut', category: 'Vegetables' },
  { emoji: '🌱', name: 'Sprouts', category: 'Vegetables' },

  // Fruits
  { emoji: '🍋', name: 'Lemon', category: 'Fruits' },
  { emoji: '🍊', name: 'Orange', category: 'Fruits' },
  { emoji: '🍎', name: 'Apple', category: 'Fruits' },
  { emoji: '🍌', name: 'Banana', category: 'Fruits' },
  { emoji: '🍓', name: 'Strawberry', category: 'Fruits' },
  { emoji: '🫐', name: 'Blueberries', category: 'Fruits' },
  { emoji: '🍇', name: 'Grapes', category: 'Fruits' },
  { emoji: '🍒', name: 'Cherry', category: 'Fruits' },
  { emoji: '🥭', name: 'Mango', category: 'Fruits' },
  { emoji: '🍍', name: 'Pineapple', category: 'Fruits' },
  { emoji: '🥝', name: 'Kiwi', category: 'Fruits' },
  { emoji: '🍈', name: 'Melon', category: 'Fruits' },
  { emoji: '🍉', name: 'Watermelon', category: 'Fruits' },
  { emoji: '🍐', name: 'Pear', category: 'Fruits' },
  { emoji: '🍑', name: 'Peach', category: 'Fruits' },
  { emoji: '🟠', name: 'Cantaloupe', category: 'Fruits' },

  // Nuts & Seeds
  { emoji: '🥜', name: 'Peanuts', category: 'Nuts & Seeds' },
  { emoji: '🌰', name: 'Chestnut', category: 'Nuts & Seeds' },
  { emoji: '🔸', name: 'Almonds', category: 'Nuts & Seeds' },
  { emoji: '🔶', name: 'Walnuts', category: 'Nuts & Seeds' },
  { emoji: '🟤', name: 'Pecans', category: 'Nuts & Seeds' },
  { emoji: '🤎', name: 'Cashews', category: 'Nuts & Seeds' },
  { emoji: '🌾', name: 'Seeds', category: 'Nuts & Seeds' },
  { emoji: '🌻', name: 'Sunflower Seeds', category: 'Nuts & Seeds' },

  // Dairy & Eggs
  { emoji: '🧈', name: 'Butter', category: 'Dairy & Eggs' },
  { emoji: '🥛', name: 'Milk', category: 'Dairy & Eggs' },
  { emoji: '🧀', name: 'Cheese', category: 'Dairy & Eggs' },
  { emoji: '🥚', name: 'Egg', category: 'Dairy & Eggs' },
  { emoji: '🍦', name: 'Ice Cream', category: 'Dairy & Eggs' },
  { emoji: '🍨', name: 'Yogurt', category: 'Dairy & Eggs' },
  { emoji: '🟡', name: 'Cream Cheese', category: 'Dairy & Eggs' },
  { emoji: '⚪', name: 'Sour Cream', category: 'Dairy & Eggs' },

  // Meat & Seafood
  { emoji: '🍖', name: 'Meat', category: 'Meat & Seafood' },
  { emoji: '🥩', name: 'Steak', category: 'Meat & Seafood' },
  { emoji: '🥓', name: 'Bacon', category: 'Meat & Seafood' },
  { emoji: '🍗', name: 'Chicken', category: 'Meat & Seafood' },
  { emoji: '🐟', name: 'Fish', category: 'Meat & Seafood' },
  { emoji: '🦐', name: 'Shrimp', category: 'Meat & Seafood' },
  { emoji: '🦞', name: 'Lobster', category: 'Meat & Seafood' },
  { emoji: '🦀', name: 'Crab', category: 'Meat & Seafood' },
  { emoji: '🐙', name: 'Octopus', category: 'Meat & Seafood' },
  { emoji: '🦑', name: 'Squid', category: 'Meat & Seafood' },
  { emoji: '🐄', name: 'Beef', category: 'Meat & Seafood' },
  { emoji: '🐷', name: 'Pork', category: 'Meat & Seafood' },
  { emoji: '🦃', name: 'Turkey', category: 'Meat & Seafood' },
  { emoji: '🐑', name: 'Lamb', category: 'Meat & Seafood' },
  { emoji: '🌭', name: 'Hot Dog', category: 'Meat & Seafood' },
  { emoji: '🍤', name: 'Fried Shrimp', category: 'Meat & Seafood' },

  // Grains & Carbs
  { emoji: '🍞', name: 'Bread', category: 'Grains & Carbs' },
  { emoji: '🥖', name: 'Baguette', category: 'Grains & Carbs' },
  { emoji: '🥨', name: 'Pretzel', category: 'Grains & Carbs' },
  { emoji: '🥯', name: 'Bagel', category: 'Grains & Carbs' },
  { emoji: '🧇', name: 'Waffle', category: 'Grains & Carbs' },
  { emoji: '🥞', name: 'Pancakes', category: 'Grains & Carbs' },
  { emoji: '🍝', name: 'Pasta', category: 'Grains & Carbs' },
  { emoji: '🍜', name: 'Noodles', category: 'Grains & Carbs' },
  { emoji: '🍲', name: 'Stew', category: 'Grains & Carbs' },
  { emoji: '🍱', name: 'Bento', category: 'Grains & Carbs' },
  { emoji: '🍘', name: 'Rice Cracker', category: 'Grains & Carbs' },
  { emoji: '🍙', name: 'Rice Ball', category: 'Grains & Carbs' },
  { emoji: '🍚', name: 'Rice', category: 'Grains & Carbs' },
  { emoji: '🍛', name: 'Curry', category: 'Grains & Carbs' },
  { emoji: '🫘', name: 'Beans', category: 'Grains & Carbs' },

  // Herbs & Seasonings
  { emoji: '🌿', name: 'Herbs', category: 'Herbs & Seasonings' },
  { emoji: '🍃', name: 'Leaves', category: 'Herbs & Seasonings' },
  { emoji: '🧂', name: 'Salt', category: 'Herbs & Seasonings' },
  { emoji: '🍯', name: 'Honey', category: 'Herbs & Seasonings' },

  // Beverages
  { emoji: '🥤', name: 'Cup with Straw', category: 'Beverages' },
  { emoji: '☕', name: 'Coffee', category: 'Beverages' },
  { emoji: '🫖', name: 'Teapot', category: 'Beverages' },
  { emoji: '🍷', name: 'Wine', category: 'Beverages' },
  { emoji: '🍺', name: 'Beer', category: 'Beverages' },
  { emoji: '🥃', name: 'Tumbler Glass', category: 'Beverages' },

  // Oils & Fats
  { emoji: '🫒', name: 'Olive Oil', category: 'Oils & Fats' },
  { emoji: '🧴', name: 'Vegetable Oil', category: 'Oils & Fats' },
  { emoji: '🥥', name: 'Coconut Oil', category: 'Oils & Fats' },
  { emoji: '💧', name: 'Cooking Oil', category: 'Oils & Fats' },
  { emoji: '🟡', name: 'MCT Oil', category: 'Oils & Fats' },

  // Condiments & Sauces
  { emoji: '🍅', name: 'Ketchup', category: 'Condiments & Sauces' },
  { emoji: '🟨', name: 'Mustard', category: 'Condiments & Sauces' },
  { emoji: '⚪', name: 'Mayonnaise', category: 'Condiments & Sauces' },
  { emoji: '🔥', name: 'Hot Sauce', category: 'Condiments & Sauces' },
  { emoji: '🟫', name: 'Soy Sauce', category: 'Condiments & Sauces' },
  { emoji: '🍶', name: 'Worcestershire', category: 'Condiments & Sauces' },
  { emoji: '🍋', name: 'Lemon Juice', category: 'Condiments & Sauces' },
  { emoji: '🔵', name: 'Balsamic Vinegar', category: 'Condiments & Sauces' },
  { emoji: '🍎', name: 'Apple Cider Vinegar', category: 'Condiments & Sauces' },
  { emoji: '🟤', name: 'BBQ Sauce', category: 'Condiments & Sauces' },
  { emoji: '🔴', name: 'Marinara Sauce', category: 'Condiments & Sauces' },
  { emoji: '🟢', name: 'Pesto', category: 'Condiments & Sauces' },

  // Canned & Jarred Goods
  { emoji: '🥫', name: 'Can', category: 'Canned & Jarred' },
  { emoji: '🫙', name: 'Jar', category: 'Canned & Jarred' },
  { emoji: '🍅', name: 'Canned Tomatoes', category: 'Canned & Jarred' },
  { emoji: '🫘', name: 'Canned Beans', category: 'Canned & Jarred' },
  { emoji: '🌽', name: 'Canned Corn', category: 'Canned & Jarred' },
  { emoji: '🐟', name: 'Canned Tuna', category: 'Canned & Jarred' },
  { emoji: '🍜', name: 'Canned Soup', category: 'Canned & Jarred' },
  { emoji: '🥒', name: 'Pickles', category: 'Canned & Jarred' },
  { emoji: '🍓', name: 'Jam', category: 'Canned & Jarred' },
  { emoji: '🥜', name: 'Peanut Butter', category: 'Canned & Jarred' },

  // Baking & Desserts
  { emoji: '🍰', name: 'Cake', category: 'Baking & Desserts' },
  { emoji: '🧁', name: 'Cupcake', category: 'Baking & Desserts' },
  { emoji: '🍪', name: 'Cookie', category: 'Baking & Desserts' },
  { emoji: '🥧', name: 'Pie', category: 'Baking & Desserts' },
  { emoji: '🍫', name: 'Chocolate', category: 'Baking & Desserts' },
  { emoji: '🍯', name: 'Honey', category: 'Baking & Desserts' },
  { emoji: '⚪', name: 'Sugar', category: 'Baking & Desserts' },
  { emoji: '🌾', name: 'Flour', category: 'Baking & Desserts' },
  { emoji: '🟤', name: 'Vanilla Extract', category: 'Baking & Desserts' },
  { emoji: '⚪', name: 'Baking Powder', category: 'Baking & Desserts' },

  // Kitchen & Misc
  {
    emoji: '🍽️',
    name: 'Fork and Knife with Plate',
    category: 'Kitchen & Misc',
  },
  { emoji: '🥄', name: 'Spoon', category: 'Kitchen & Misc' },
  { emoji: '🔪', name: 'Kitchen Knife', category: 'Kitchen & Misc' },
  { emoji: '🫗', name: 'Pouring Liquid', category: 'Kitchen & Misc' },
  { emoji: '📦', name: 'Package', category: 'Kitchen & Misc' },
  { emoji: '🫙', name: 'Bottle', category: 'Kitchen & Misc' },
  ];
  
  // Add emojis to map, keeping the first occurrence
  allEmojis.forEach(item => {
    if (!emojiMap.has(item.emoji)) {
      emojiMap.set(item.emoji, item);
    }
  });
  
  // Return array sorted by category and name
  return Array.from(emojiMap.values()).sort((a, b) => {
    if (a.category !== b.category) {
      return a.category.localeCompare(b.category);
    }
    return a.name.localeCompare(b.name);
  });
};

export const UNIQUE_FOOD_EMOJIS = createUniqueEmojis();

// Keep original for backward compatibility
export const FOOD_EMOJIS: FoodEmoji[] = [
  // Vegetables
  { emoji: '🥗', name: 'Salad', category: 'Vegetables' },
  { emoji: '🍅', name: 'Tomato', category: 'Vegetables' },
  { emoji: '🥕', name: 'Carrot', category: 'Vegetables' },
  { emoji: '🌽', name: 'Corn', category: 'Vegetables' },
  { emoji: '🥒', name: 'Cucumber', category: 'Vegetables' },
  { emoji: '🥬', name: 'Leafy Greens', category: 'Vegetables' },
  { emoji: '🥔', name: 'Potato', category: 'Vegetables' },
  { emoji: '🍠', name: 'Sweet Potato', category: 'Vegetables' },
  { emoji: '🧄', name: 'Garlic', category: 'Vegetables' },
  { emoji: '🧅', name: 'Onion', category: 'Vegetables' },
  { emoji: '🫒', name: 'Olive', category: 'Vegetables' },
  { emoji: '🌶️', name: 'Hot Pepper', category: 'Vegetables' },
  { emoji: '🫑', name: 'Bell Pepper', category: 'Vegetables' },
  { emoji: '🥑', name: 'Avocado', category: 'Vegetables' },
  { emoji: '🍆', name: 'Eggplant', category: 'Vegetables' },
  { emoji: '🥦', name: 'Broccoli', category: 'Vegetables' },
  { emoji: '🫛', name: 'Pea Pod', category: 'Vegetables' },
  { emoji: '🍄', name: 'Mushroom', category: 'Vegetables' },
  { emoji: '🥥', name: 'Coconut', category: 'Vegetables' },
  { emoji: '🌱', name: 'Sprouts', category: 'Vegetables' },

  // Fruits
  { emoji: '🍋', name: 'Lemon', category: 'Fruits' },
  { emoji: '🍊', name: 'Orange', category: 'Fruits' },
  { emoji: '🍎', name: 'Apple', category: 'Fruits' },
  { emoji: '🍌', name: 'Banana', category: 'Fruits' },
  { emoji: '🍓', name: 'Strawberry', category: 'Fruits' },
  { emoji: '🫐', name: 'Blueberries', category: 'Fruits' },
  { emoji: '🍇', name: 'Grapes', category: 'Fruits' },
  { emoji: '🍒', name: 'Cherry', category: 'Fruits' },
  { emoji: '🥭', name: 'Mango', category: 'Fruits' },
  { emoji: '🍍', name: 'Pineapple', category: 'Fruits' },
  { emoji: '🥝', name: 'Kiwi', category: 'Fruits' },
  { emoji: '🍈', name: 'Melon', category: 'Fruits' },
  { emoji: '🍉', name: 'Watermelon', category: 'Fruits' },
  { emoji: '🍐', name: 'Pear', category: 'Fruits' },
  { emoji: '🍑', name: 'Peach', category: 'Fruits' },
  { emoji: '🟠', name: 'Cantaloupe', category: 'Fruits' },

  // Nuts & Seeds
  { emoji: '🥜', name: 'Peanuts', category: 'Nuts & Seeds' },
  { emoji: '🌰', name: 'Chestnut', category: 'Nuts & Seeds' },
  { emoji: '🔸', name: 'Almonds', category: 'Nuts & Seeds' },
  { emoji: '🔶', name: 'Walnuts', category: 'Nuts & Seeds' },
  { emoji: '🟤', name: 'Pecans', category: 'Nuts & Seeds' },
  { emoji: '🤎', name: 'Cashews', category: 'Nuts & Seeds' },
  { emoji: '🌾', name: 'Seeds', category: 'Nuts & Seeds' },
  { emoji: '🌻', name: 'Sunflower Seeds', category: 'Nuts & Seeds' },

  // Dairy & Eggs
  { emoji: '🧈', name: 'Butter', category: 'Dairy & Eggs' },
  { emoji: '🥛', name: 'Milk', category: 'Dairy & Eggs' },
  { emoji: '🧀', name: 'Cheese', category: 'Dairy & Eggs' },
  { emoji: '🥚', name: 'Egg', category: 'Dairy & Eggs' },
  { emoji: '🍦', name: 'Ice Cream', category: 'Dairy & Eggs' },
  { emoji: '🍨', name: 'Yogurt', category: 'Dairy & Eggs' },
  { emoji: '🟡', name: 'Cream Cheese', category: 'Dairy & Eggs' },
  { emoji: '⚪', name: 'Sour Cream', category: 'Dairy & Eggs' },

  // Meat & Seafood
  { emoji: '🍖', name: 'Meat', category: 'Meat & Seafood' },
  { emoji: '🥩', name: 'Steak', category: 'Meat & Seafood' },
  { emoji: '🥓', name: 'Bacon', category: 'Meat & Seafood' },
  { emoji: '🍗', name: 'Chicken', category: 'Meat & Seafood' },
  { emoji: '🐟', name: 'Fish', category: 'Meat & Seafood' },
  { emoji: '🦐', name: 'Shrimp', category: 'Meat & Seafood' },
  { emoji: '🦞', name: 'Lobster', category: 'Meat & Seafood' },
  { emoji: '🦀', name: 'Crab', category: 'Meat & Seafood' },
  { emoji: '🐙', name: 'Octopus', category: 'Meat & Seafood' },
  { emoji: '🦑', name: 'Squid', category: 'Meat & Seafood' },
  { emoji: '🐄', name: 'Beef', category: 'Meat & Seafood' },
  { emoji: '🐷', name: 'Pork', category: 'Meat & Seafood' },
  { emoji: '🦃', name: 'Turkey', category: 'Meat & Seafood' },
  { emoji: '🐑', name: 'Lamb', category: 'Meat & Seafood' },
  { emoji: '🌭', name: 'Hot Dog', category: 'Meat & Seafood' },
  { emoji: '🍤', name: 'Fried Shrimp', category: 'Meat & Seafood' },

  // Grains & Carbs
  { emoji: '🍞', name: 'Bread', category: 'Grains & Carbs' },
  { emoji: '🥖', name: 'Baguette', category: 'Grains & Carbs' },
  { emoji: '🥨', name: 'Pretzel', category: 'Grains & Carbs' },
  { emoji: '🥯', name: 'Bagel', category: 'Grains & Carbs' },
  { emoji: '🧇', name: 'Waffle', category: 'Grains & Carbs' },
  { emoji: '🥞', name: 'Pancakes', category: 'Grains & Carbs' },
  { emoji: '🍝', name: 'Pasta', category: 'Grains & Carbs' },
  { emoji: '🍜', name: 'Noodles', category: 'Grains & Carbs' },
  { emoji: '🍲', name: 'Stew', category: 'Grains & Carbs' },
  { emoji: '🍱', name: 'Bento', category: 'Grains & Carbs' },
  { emoji: '🍘', name: 'Rice Cracker', category: 'Grains & Carbs' },
  { emoji: '🍙', name: 'Rice Ball', category: 'Grains & Carbs' },
  { emoji: '🍚', name: 'Rice', category: 'Grains & Carbs' },
  { emoji: '🍛', name: 'Curry', category: 'Grains & Carbs' },
  { emoji: '🫘', name: 'Beans', category: 'Grains & Carbs' },

  // Herbs & Seasonings
  { emoji: '🌿', name: 'Herbs', category: 'Herbs & Seasonings' },
  { emoji: '🍃', name: 'Leaves', category: 'Herbs & Seasonings' },
  { emoji: '🧂', name: 'Salt', category: 'Herbs & Seasonings' },
  { emoji: '🍯', name: 'Honey', category: 'Herbs & Seasonings' },

  // Beverages
  { emoji: '🥤', name: 'Cup with Straw', category: 'Beverages' },
  { emoji: '☕', name: 'Coffee', category: 'Beverages' },
  { emoji: '🫖', name: 'Teapot', category: 'Beverages' },
  { emoji: '🍷', name: 'Wine', category: 'Beverages' },
  { emoji: '🍺', name: 'Beer', category: 'Beverages' },
  { emoji: '🥃', name: 'Tumbler Glass', category: 'Beverages' },

  // Oils & Fats
  { emoji: '🫒', name: 'Olive Oil', category: 'Oils & Fats' },
  { emoji: '🧴', name: 'Vegetable Oil', category: 'Oils & Fats' },
  { emoji: '🥥', name: 'Coconut Oil', category: 'Oils & Fats' },
  { emoji: '💧', name: 'Cooking Oil', category: 'Oils & Fats' },
  { emoji: '🟡', name: 'MCT Oil', category: 'Oils & Fats' },

  // Condiments & Sauces
  { emoji: '🍅', name: 'Ketchup', category: 'Condiments & Sauces' },
  { emoji: '🟨', name: 'Mustard', category: 'Condiments & Sauces' },
  { emoji: '⚪', name: 'Mayonnaise', category: 'Condiments & Sauces' },
  { emoji: '🔥', name: 'Hot Sauce', category: 'Condiments & Sauces' },
  { emoji: '🟫', name: 'Soy Sauce', category: 'Condiments & Sauces' },
  { emoji: '🍶', name: 'Worcestershire', category: 'Condiments & Sauces' },
  { emoji: '🍋', name: 'Lemon Juice', category: 'Condiments & Sauces' },
  { emoji: '🔵', name: 'Balsamic Vinegar', category: 'Condiments & Sauces' },
  { emoji: '🍎', name: 'Apple Cider Vinegar', category: 'Condiments & Sauces' },
  { emoji: '🟤', name: 'BBQ Sauce', category: 'Condiments & Sauces' },
  { emoji: '🔴', name: 'Marinara Sauce', category: 'Condiments & Sauces' },
  { emoji: '🟢', name: 'Pesto', category: 'Condiments & Sauces' },

  // Canned & Jarred Goods
  { emoji: '🥫', name: 'Can', category: 'Canned & Jarred' },
  { emoji: '🫙', name: 'Jar', category: 'Canned & Jarred' },
  { emoji: '🍅', name: 'Canned Tomatoes', category: 'Canned & Jarred' },
  { emoji: '🫘', name: 'Canned Beans', category: 'Canned & Jarred' },
  { emoji: '🌽', name: 'Canned Corn', category: 'Canned & Jarred' },
  { emoji: '🐟', name: 'Canned Tuna', category: 'Canned & Jarred' },
  { emoji: '🍜', name: 'Canned Soup', category: 'Canned & Jarred' },
  { emoji: '🥒', name: 'Pickles', category: 'Canned & Jarred' },
  { emoji: '🍓', name: 'Jam', category: 'Canned & Jarred' },
  { emoji: '🥜', name: 'Peanut Butter', category: 'Canned & Jarred' },

  // Baking & Desserts
  { emoji: '🍰', name: 'Cake', category: 'Baking & Desserts' },
  { emoji: '🧁', name: 'Cupcake', category: 'Baking & Desserts' },
  { emoji: '🍪', name: 'Cookie', category: 'Baking & Desserts' },
  { emoji: '🥧', name: 'Pie', category: 'Baking & Desserts' },
  { emoji: '🍫', name: 'Chocolate', category: 'Baking & Desserts' },
  { emoji: '🍯', name: 'Honey', category: 'Baking & Desserts' },
  { emoji: '⚪', name: 'Sugar', category: 'Baking & Desserts' },
  { emoji: '🌾', name: 'Flour', category: 'Baking & Desserts' },
  { emoji: '🟤', name: 'Vanilla Extract', category: 'Baking & Desserts' },
  { emoji: '⚪', name: 'Baking Powder', category: 'Baking & Desserts' },

  // Kitchen & Misc
  {
    emoji: '🍽️',
    name: 'Fork and Knife with Plate',
    category: 'Kitchen & Misc',
  },
  { emoji: '🥄', name: 'Spoon', category: 'Kitchen & Misc' },
  { emoji: '🔪', name: 'Kitchen Knife', category: 'Kitchen & Misc' },
  { emoji: '🫗', name: 'Pouring Liquid', category: 'Kitchen & Misc' },
  { emoji: '📦', name: 'Package', category: 'Kitchen & Misc' },
  { emoji: '🫙', name: 'Bottle', category: 'Kitchen & Misc' },
];

// Helper functions
export const getEmojisByCategory = () => {
  const categories: Record<string, FoodEmoji[]> = {};

  FOOD_EMOJIS.forEach((item) => {
    if (!categories[item.category]) {
      categories[item.category] = [];
    }
    categories[item.category].push(item);
  });

  return categories;
};

export const findEmojiByEmoji = (emoji: string): FoodEmoji | undefined => {
  return UNIQUE_FOOD_EMOJIS.find((item) => item.emoji === emoji);
};

export const getRandomFoodEmoji = (): FoodEmoji => {
  return UNIQUE_FOOD_EMOJIS[Math.floor(Math.random() * UNIQUE_FOOD_EMOJIS.length)];
};

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@supabase/supabase-js';
import { supabase } from './supabase';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const router = useRouter();

  const checkAdminRole = (user: User | null) => {
    if (!user) return false;
    // Check if user has admin role in app metadata
    const userRole = user.app_metadata?.user_role;
    return userRole === 'admin';
  };

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      const currentUser = session?.user ?? null;
      setUser(currentUser);
      setIsAdmin(checkAdminRole(currentUser));
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session?.user?.email);
      const currentUser = session?.user ?? null;
      setUser(currentUser);
      setIsAdmin(checkAdminRole(currentUser));
      setLoading(false);

      // Handle specific auth events
      if (event === 'SIGNED_IN' && currentUser) {
        console.log(
          'User signed in:',
          currentUser.email,
          'isAdmin:',
          checkAdminRole(currentUser)
        );
        // Let the useEffect in components handle the routing
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out, redirecting to auth');
        router.replace('/auth');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [router]);

  const signOut = async () => {
    await supabase.auth.signOut();
    // Force redirect to auth page after logout
    router.replace('/auth');
  };

  return {
    user,
    loading,
    isAdmin,
    signOut,
  };
}

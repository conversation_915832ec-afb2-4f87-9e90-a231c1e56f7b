'use client';

import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { supabase } from '@/lib/supabase';
import { useMutation, useQuery } from '@tanstack/react-query';
import Fuse from 'fuse.js';
import {
  AlertTriangle,
  Check,
  CheckSquare,
  ChevronDown,
  Eye,
  EyeOff,
  FileText,
  Loader2,
  Merge,
  Package,
  Plus,
  RefreshCw,
  Search,
  Target,
  X,
  Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

interface BadIngredient {
  name: string;
  usedInMeals: {
    id: string;
    name: string;
  }[];
  sampleContexts: {
    originalText: string;
    mealId: string;
    mealName: string;
  }[];
  totalOccurrences: number;
  masterIngredientMatch?: {
    id: string;
    name: string;
    emoji: string | null;
  };
}

interface SuggestedMatch {
  badIngredient: BadIngredient;
  matches: {
    id: string;
    name: string;
    emoji: string | null;
    similarity: number;
  }[];
}

interface MergeOperation {
  fromIngredientName: string;
  toIngredientId: string;
  toIngredientName: string;
  affectedMeals: {
    id: string;
    name: string;
  }[];
}

interface SplitOperation {
  fromIngredientName: string;
  splitInto: string[];
  affectedMeals: {
    id: string;
    name: string;
  }[];
}

interface SplitSuggestion {
  original: string;
  splitInto: string[];
  pattern: string;
}

// Function to detect ingredient patterns for bulk processing
function detectIngredientPattern(ingredientName: string): string | null {
  const name = ingredientName.toLowerCase().trim();

  // Common patterns for grouping similar ingredients
  if (name.includes('salt') && name.includes('pepper')) {
    return 'salt-and-pepper';
  }
  if (name.includes('olive oil')) {
    return 'olive-oil';
  }
  if (
    name.includes('garlic') &&
    (name.includes('clove') || name.includes('minced'))
  ) {
    return 'garlic';
  }
  if (name.includes('onion') && !name.includes('powder')) {
    return 'onion';
  }
  if (
    name.includes('tomato') &&
    (name.includes('can') || name.includes('sauce'))
  ) {
    return 'canned-tomato';
  }
  if (name.includes('cheese') && name.includes('shredded')) {
    return 'shredded-cheese';
  }
  if (name.includes('butter') && !name.includes('peanut')) {
    return 'butter';
  }
  if (
    name.includes('ground') &&
    (name.includes('beef') ||
      name.includes('turkey') ||
      name.includes('chicken'))
  ) {
    return 'ground-meat';
  }
  if (
    name.includes('black pepper') ||
    (name.includes('pepper') && name.includes('ground'))
  ) {
    return 'black-pepper';
  }

  return null;
}

// Function to detect and suggest ingredient splits
function detectSplitSuggestion(ingredientName: string): SplitSuggestion | null {
  const name = ingredientName.trim().toLowerCase();

  // Split patterns with their descriptors
  const patterns = [
    {
      regex: /^(.+?)\s+and\s+(.+?)(?:\s+to\s+taste)?$/i,
      name: 'X and Y',
    },
    {
      regex: /^(.+?)\s+&\s+(.+?)(?:\s+to\s+taste)?$/i,
      name: 'X & Y',
    },
    {
      regex: /^(.+?),\s*(.+?)(?:\s+to\s+taste)?$/i,
      name: 'X, Y',
    },
  ];

  for (const pattern of patterns) {
    const match = name.match(pattern.regex);
    if (match) {
      const [, part1, part2] = match;

      // Clean each part by removing common descriptors
      const cleanPart = (part: string) => {
        return part
          .replace(/\b(freshly|finely|coarsely|roughly)\s+/gi, '') // Remove preparation descriptors
          .replace(/\b(ground|chopped|minced|diced|sliced)\s+/gi, '') // Remove preparation methods
          .replace(/\b(fresh|dried|organic|kosher)\s+/gi, '') // Remove quality descriptors
          .trim();
      };

      const cleaned1 = cleanPart(part1);
      const cleaned2 = cleanPart(part2);

      // Only suggest split if both parts are meaningful ingredients (not empty or too short)
      if (
        cleaned1.length >= 3 &&
        cleaned2.length >= 3 &&
        cleaned1 !== cleaned2 && // Not the same ingredient
        !cleaned1.match(/^\d/) &&
        !cleaned2.match(/^\d/)
      ) {
        // Don't start with numbers

        return {
          original: ingredientName,
          splitInto: [cleaned1, cleaned2],
          pattern: pattern.name,
        };
      }
    }
  }

  return null;
}

// Fast inline editing row component
function FastEditRow({
  badIngredient,
  onMerge,
  onReview,
  onSkip,
  onSplit,
  allIngredients,
  suggestedMatches,
  isSelected,
  onSelectionChange,
  patternGroup,
}: {
  badIngredient: BadIngredient;
  onMerge: (
    fromName: string,
    toId: string,
    toName: string,
    meals: { id: string; name: string }[]
  ) => void;
  onReview: (ingredientName: string) => void;
  onSkip: (ingredientName: string) => void;
  onSplit: (
    fromName: string,
    splitInto: string[],
    meals: { id: string; name: string }[]
  ) => void;
  allIngredients: { id: string; name: string; emoji: string | null }[];
  suggestedMatches: {
    id: string;
    name: string;
    emoji: string | null;
    similarity: number;
  }[];
  isSelected?: boolean;
  onSelectionChange?: (selected: boolean) => void;
  patternGroup?: string | null;
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isProcessed, setIsProcessed] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [newIngredientName, setNewIngredientName] = useState('');
  const [showCreateNew, setShowCreateNew] = useState(false);
  const [showContexts, setShowContexts] = useState(false);
  const [showSplitSuggestion, setShowSplitSuggestion] = useState(false);
  const [customSplitIngredients, setCustomSplitIngredients] = useState<
    string[]
  >([]);
  const searchRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Detect if this ingredient can be split
  const splitSuggestion = detectSplitSuggestion(badIngredient.name);

  // Auto-focus search when row becomes active
  useEffect(() => {
    if (showDropdown && searchRef.current) {
      searchRef.current.focus();
    }
  }, [showDropdown]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter ingredients based on search
  const filteredIngredients = searchTerm
    ? allIngredients
        .filter((ingredient) =>
          ingredient.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .slice(0, 8)
    : suggestedMatches.slice(0, 6); // Show top suggestions by default

  const handleMergeToIngredient = (ingredient: {
    id: string;
    name: string;
    emoji: string | null;
  }) => {
    onMerge(
      badIngredient.name,
      ingredient.id,
      ingredient.name,
      badIngredient.usedInMeals
    );
    setIsProcessed(true);
    setShowDropdown(false);
  };

  const handleCreateAndMerge = async () => {
    if (!newIngredientName.trim()) return;

    try {
      const { data: newIngredient, error } = await supabase
        .from('ingredients')
        .insert({
          name: newIngredientName.trim(),
          emoji: '🥗',
          category: null,
        })
        .select()
        .single();

      if (error) throw error;

      onMerge(
        badIngredient.name,
        newIngredient.id,
        newIngredient.name,
        badIngredient.usedInMeals
      );
      setIsProcessed(true);
      setShowDropdown(false);
      setNewIngredientName('');
      setShowCreateNew(false);
    } catch (error) {
      console.error('Error creating ingredient:', error);
      alert('Failed to create ingredient');
    }
  };

  const handleReview = () => {
    onReview(badIngredient.name);
    setIsProcessed(true);
  };

  const handleSkip = () => {
    onSkip(badIngredient.name);
    setIsProcessed(true);
  };

  const handleSplitWithSuggestion = () => {
    if (splitSuggestion) {
      onSplit(
        badIngredient.name,
        splitSuggestion.splitInto,
        badIngredient.usedInMeals
      );
      setIsProcessed(true);
    }
  };

  const handleCustomSplit = () => {
    const validIngredients = customSplitIngredients
      .map((name) => name.trim())
      .filter((name) => name.length >= 2);

    if (validIngredients.length >= 2) {
      onSplit(badIngredient.name, validIngredients, badIngredient.usedInMeals);
      setIsProcessed(true);
      setShowSplitSuggestion(false);
    }
  };

  const handleToggleSplitView = () => {
    setShowSplitSuggestion(!showSplitSuggestion);
    if (splitSuggestion && !showSplitSuggestion) {
      setCustomSplitIngredients([...splitSuggestion.splitInto]);
    }
  };

  if (isProcessed) {
    return (
      <div className="flex items-center justify-between p-3 bg-green-900/20 border border-green-500/30 rounded-lg opacity-60">
        <div className="flex items-center space-x-3">
          <Check className="h-5 w-5 text-green-400" />
          <span className="text-green-400 font-medium">
            {badIngredient.name}
          </span>
          <Badge
            variant="outline"
            className="text-green-400 border-green-500/50"
          >
            Processed
          </Badge>
        </div>
        <div className="text-right">
          <span className="text-xs text-green-400">
            {badIngredient.usedInMeals.length} meals
          </span>
          <br />
          <span className="text-xs text-amber-400 font-medium">
            {badIngredient.totalOccurrences} total
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={`flex items-center justify-between p-3 hover:bg-slate-800/30 border rounded-lg transition-colors ${
          isSelected
            ? 'border-blue-500 bg-blue-900/20'
            : patternGroup
              ? 'border-amber-500/50 bg-amber-900/10'
              : 'border-slate-700'
        }`}
      >
        <div className="flex items-center space-x-3 flex-1">
          {onSelectionChange && (
            <button
              onClick={() => onSelectionChange(!(isSelected || false))}
              className="w-5 h-5 border border-slate-600 rounded bg-slate-700/50 hover:bg-slate-600/50 flex items-center justify-center transition-colors"
            >
              {isSelected && <Check className="h-3 w-3 text-blue-400" />}
            </button>
          )}
          <span className="text-lg">🥗</span>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <div className="font-medium text-white">{badIngredient.name}</div>
              {patternGroup && (
                <div className="flex items-center space-x-1 px-2 py-1 bg-amber-600/20 border border-amber-500/30 rounded-md">
                  <Package className="h-3 w-3 text-amber-400" />
                  <span className="text-xs text-amber-400 font-medium">
                    {patternGroup.replace('-', ' ').toUpperCase()}
                  </span>
                </div>
              )}
              {badIngredient.masterIngredientMatch && (
                <div className="flex items-center space-x-1 px-2 py-1 bg-green-600/20 border border-green-500/30 rounded-md">
                  <Check className="h-3 w-3 text-green-400" />
                  <span className="text-xs text-green-400 font-medium">
                    EXISTS: {badIngredient.masterIngredientMatch.name}
                  </span>
                </div>
              )}
            </div>
            <div className="text-xs text-slate-400">
              Used in {badIngredient.usedInMeals.length} meal
              {badIngredient.usedInMeals.length !== 1 ? 's' : ''}
              {badIngredient.sampleContexts.length > 0 && (
                <span className="ml-2 text-slate-500">•</span>
              )}
              {badIngredient.sampleContexts.length > 0 && (
                <span className="ml-1 text-slate-500">
                  {badIngredient.sampleContexts.length} context
                  {badIngredient.sampleContexts.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Quick action buttons for top suggestions */}
          {suggestedMatches.slice(0, 3).map((suggestion) => (
            <button
              key={suggestion.id}
              onClick={() => handleMergeToIngredient(suggestion)}
              className="flex items-center space-x-2 px-3 py-1.5 bg-blue-600/20 border border-blue-500/30 rounded-md hover:bg-blue-600/30 transition-colors text-sm"
              title={`Merge to ${suggestion.name}`}
            >
              <span className="text-xs">{suggestion.emoji || '🥗'}</span>
              <span className="text-blue-400">{suggestion.name}</span>
              <span className="text-xs text-blue-300">
                ({(suggestion.similarity * 100).toFixed(0)}%)
              </span>
              <Zap className="h-3 w-3 text-blue-400" />
            </button>
          ))}

          {/* Show contexts button */}
          {badIngredient.sampleContexts.length > 0 && (
            <Button
              onClick={() => setShowContexts(!showContexts)}
              variant="outline"
              size="sm"
              className="bg-purple-600/20 border-purple-500/30 text-purple-400 hover:bg-purple-600/30"
              title="Show sample contexts from meals"
            >
              {showContexts ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              <span className="ml-1 hidden sm:inline">Contexts</span>
            </Button>
          )}

          {/* Dropdown trigger */}
          <Button
            onClick={() => setShowDropdown(!showDropdown)}
            variant="outline"
            size="sm"
            className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
          >
            <Search className="h-4 w-4 mr-1" />
            Search
            <ChevronDown
              className={`h-3 w-3 ml-1 transition-transform ${
                showDropdown ? 'rotate-180' : ''
              }`}
            />
          </Button>

          {/* Split button - show if ingredient can be split */}
          {splitSuggestion && (
            <Button
              onClick={handleToggleSplitView}
              variant="outline"
              size="sm"
              className="bg-orange-600/20 border-orange-500/30 text-orange-400 hover:bg-orange-600/30"
              title={`Split into: ${splitSuggestion.splitInto.join(' + ')}`}
            >
              <Plus className="h-4 w-4 mr-1" />
              Split
            </Button>
          )}

          {/* Action buttons */}
          <Button
            onClick={handleSkip}
            variant="outline"
            size="sm"
            className="bg-yellow-600/20 border-yellow-500/30 text-yellow-400 hover:bg-yellow-600/30"
            title="Skip this ingredient"
          >
            Skip
          </Button>
          <Button
            onClick={handleReview}
            variant="outline"
            size="sm"
            className="bg-yellow-600/20 border-yellow-500/30 text-yellow-400 hover:bg-yellow-600/30"
            title="Mark this ingredient for review"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Sample contexts display */}
      {showContexts && badIngredient.sampleContexts.length > 0 && (
        <div className="mt-3 p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-white">Sample Contexts</h4>
            <Badge
              variant="outline"
              className="text-xs text-slate-400 border-slate-600"
            >
              {badIngredient.sampleContexts.length} example
              {badIngredient.sampleContexts.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <div className="flex flex-wrap gap-2">
            {badIngredient.sampleContexts.map((context, index) => (
              <div
                key={index}
                className="inline-flex items-center px-3 py-1.5 bg-slate-900/50 border border-slate-700 rounded-full text-sm"
              >
                <span className="text-white font-mono mr-2">
                  &quot;{context.originalText}&quot;
                </span>
                <Link
                  href={`/meals/${context.mealId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 underline text-xs"
                >
                  {context.mealName}
                </Link>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Split ingredient interface */}
      {showSplitSuggestion && splitSuggestion && (
        <div className="mt-3 p-4 bg-orange-900/20 border border-orange-500/30 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-orange-400">
              Split Ingredient
            </h4>
            <Badge
              variant="outline"
              className="text-xs text-orange-400 border-orange-500/30"
            >
              {splitSuggestion.pattern}
            </Badge>
          </div>

          <div className="space-y-3">
            <div className="text-sm text-slate-300">
              Split{' '}
              <span className="font-mono text-white">
                &quot;{badIngredient.name}&quot;
              </span>{' '}
              into:
            </div>

            <div className="space-y-2">
              {customSplitIngredients.map((ingredient, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    value={ingredient}
                    onChange={(e) => {
                      const updated = [...customSplitIngredients];
                      updated[index] = e.target.value;
                      setCustomSplitIngredients(updated);
                    }}
                    className="flex-1 bg-slate-700/50 border-slate-600 text-white text-sm"
                    placeholder={`Ingredient ${index + 1}`}
                  />
                  {customSplitIngredients.length > 2 && (
                    <Button
                      onClick={() => {
                        const updated = customSplitIngredients.filter(
                          (_, i) => i !== index
                        );
                        setCustomSplitIngredients(updated);
                      }}
                      variant="outline"
                      size="sm"
                      className="bg-red-600/20 border-red-500/30 text-red-400 hover:bg-red-600/30"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between pt-2">
              <Button
                onClick={() => {
                  setCustomSplitIngredients([...customSplitIngredients, '']);
                }}
                variant="outline"
                size="sm"
                className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add ingredient
              </Button>

              <div className="flex space-x-2">
                <Button
                  onClick={() => setShowSplitSuggestion(false)}
                  variant="outline"
                  size="sm"
                  className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCustomSplit}
                  disabled={
                    customSplitIngredients.filter(
                      (name) => name.trim().length >= 2
                    ).length < 2
                  }
                  size="sm"
                  className="bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
                >
                  Split into{' '}
                  {
                    customSplitIngredients.filter(
                      (name) => name.trim().length >= 2
                    ).length
                  }{' '}
                  ingredients
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Dropdown search interface */}
      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50 max-h-80 overflow-hidden">
          <div className="p-3 border-b border-slate-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                ref={searchRef}
                type="text"
                placeholder="Search ingredients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Create new option */}
            <div className="mt-2">
              <button
                onClick={() => setShowCreateNew(!showCreateNew)}
                className="flex items-center space-x-2 px-2 py-1 rounded text-xs text-blue-400 hover:bg-slate-700/50"
              >
                <Plus className="h-3 w-3" />
                <span>
                  {showCreateNew ? 'Cancel' : 'Create new ingredient'}
                </span>
              </button>

              {showCreateNew && (
                <div className="mt-2 flex space-x-2">
                  <input
                    type="text"
                    placeholder="New ingredient name"
                    value={newIngredientName}
                    onChange={(e) => setNewIngredientName(e.target.value)}
                    className="flex-1 px-2 py-1 bg-slate-700/50 border border-slate-600 rounded text-white text-xs"
                    onKeyPress={(e) =>
                      e.key === 'Enter' && handleCreateAndMerge()
                    }
                  />
                  <button
                    onClick={handleCreateAndMerge}
                    disabled={!newIngredientName.trim()}
                    className="px-2 py-1 bg-blue-600 text-white rounded text-xs disabled:opacity-50"
                  >
                    Create
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto">
            {filteredIngredients.length > 0 ? (
              filteredIngredients.map((ingredient) => (
                <button
                  key={ingredient.id}
                  onClick={() => handleMergeToIngredient(ingredient)}
                  className="w-full flex items-center justify-between px-3 py-2 hover:bg-slate-700/50 text-left transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{ingredient.emoji || '🥗'}</span>
                    <span className="text-white">{ingredient.name}</span>
                  </div>
                  {suggestedMatches.find((s) => s.id === ingredient.id) && (
                    <Badge
                      variant="outline"
                      className="text-xs text-green-400 border-green-500/50"
                    >
                      {(
                        suggestedMatches.find((s) => s.id === ingredient.id)!
                          .similarity * 100
                      ).toFixed(0)}
                      %
                    </Badge>
                  )}
                </button>
              ))
            ) : (
              <div className="p-3 text-center text-slate-400 text-sm">
                No ingredients found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function BadIngredientsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [mergeOperations, setMergeOperations] = useState<MergeOperation[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(35); // Balanced for performance and bulk processing
  const [processedIngredients, setProcessedIngredients] = useState<Set<string>>(
    new Set()
  );
  const [selectedIngredients, setSelectedIngredients] = useState<Set<string>>(
    new Set()
  );
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showBulkMergeModal, setShowBulkMergeModal] = useState(false);
  const [bulkMergeSearch, setBulkMergeSearch] = useState('');
  const [selectedMasterIngredient, setSelectedMasterIngredient] = useState<{
    id: string;
    name: string;
    emoji: string | null;
  } | null>(null);
  const [isBulkMerging, setIsBulkMerging] = useState(false);

  // Debounced search
  const [debouncedSearch, setDebouncedSearch] = useState(searchTerm);
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      setCurrentPage(1); // Reset to first page when searching
    }, 300);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch ingredients marked for review
  const { data: reviewIngredients, refetch: refetchReviewIngredients } =
    useQuery({
      queryKey: ['ingredients-for-review'],
      queryFn: async () => {
        const response = await fetch('/api/ingredients-for-review');
        if (!response.ok) {
          throw new Error('Failed to fetch review ingredients');
        }
        const result = await response.json();
        return result.data || [];
      },
    });

  // Fetch bad ingredients (JSONB ingredients that don't match master ingredients)
  const {
    data: badIngredientsData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['bad-ingredients-v2', currentPage, pageSize, debouncedSearch], // v2 to bust cache
    queryFn: async (): Promise<{
      ingredients: BadIngredient[];
      totalCount: number;
    }> => {
      try {
        // Get all meals with their JSONB ingredients
        const { data: meals, error: mealsError } = await supabase
          .from('meals')
          .select('id, name, ingredients')
          .not('ingredients', 'is', null);

        if (mealsError) {
          console.error('Error fetching meals:', mealsError);
          throw mealsError;
        }

        // Get all master ingredient names for comparison
        const { data: masterIngredients, error: masterError } = await supabase
          .from('ingredients')
          .select('id, name, emoji');

        if (masterError) {
          console.error('Error fetching master ingredients:', masterError);
          throw masterError;
        }

        // Get ingredients currently marked for review
        const { data: reviewIngredients, error: reviewError } = await supabase
          .from('ingredients_for_review')
          .select('ingredient_name')
          .is('processed_at', null);

        if (reviewError) {
          console.error('Error fetching review ingredients:', reviewError);
          throw reviewError;
        }

        // Create a Set of ingredient names that are in review (case-insensitive)
        const reviewIngredientsSet = new Set(
          reviewIngredients.map((item) => item.ingredient_name.toLowerCase())
        );

        // Create a Map of master ingredient names (case-insensitive) to their data
        const masterIngredientsMap = new Map();
        masterIngredients.forEach((ingredient) => {
          masterIngredientsMap.set(ingredient.name.toLowerCase(), ingredient);
        });

        // Extract all unique JSONB ingredient names and track which meals use them
        const jsonbIngredientUsage = new Map<
          string,
          { id: string; name: string }[]
        >();
        const jsonbIngredientContexts = new Map<
          string,
          { originalText: string; mealId: string; mealName: string }[]
        >();
        const jsonbIngredientOccurrences = new Map<string, number>();

        meals.forEach((meal) => {
          if (!meal.ingredients) return;

          let ingredients: any[] = [];

          // Handle ALL possible JSONB formats from backup table
          if (Array.isArray(meal.ingredients)) {
            ingredients = meal.ingredients;
          } else if (typeof meal.ingredients === 'string') {
            // Legacy string format - split by newlines and treat each line as an ingredient
            ingredients = meal.ingredients
              .split('\n')
              .map((line) => line.trim())
              .filter(Boolean);
          } else if (typeof meal.ingredients === 'object') {
            // Handle object format
            ingredients = [meal.ingredients];
          }

          ingredients.forEach((ingredient: any) => {
            let ingredientName = '';
            let originalText = '';

            if (typeof ingredient === 'string') {
              // String ingredient
              ingredientName = ingredient.trim();
              originalText = ingredient;
            } else if (ingredient && typeof ingredient === 'object') {
              if (ingredient.name) {
                // Object with name property
                ingredientName = ingredient.name.trim();
                originalText = ingredient.name;
              } else if (ingredient.ingredient) {
                // Object with ingredient property
                ingredientName = ingredient.ingredient.trim();
                originalText = ingredient.ingredient;
              }
            }

            // Include ALL ingredients, even if they seem nonsensical
            // The whole point is to see everything raw and unprocessed
            if (ingredientName && ingredientName.length > 0) {
              if (!jsonbIngredientUsage.has(ingredientName)) {
                jsonbIngredientUsage.set(ingredientName, []);
                jsonbIngredientContexts.set(ingredientName, []);
                jsonbIngredientOccurrences.set(ingredientName, 0);
              }

              // Track unique meals (don't duplicate meal entries for same ingredient in same meal)
              const existingMeals = jsonbIngredientUsage.get(ingredientName)!;
              if (!existingMeals.find((m) => m.id === meal.id)) {
                existingMeals.push({
                  id: meal.id,
                  name: meal.name,
                });
              }

              // Count every occurrence (even duplicates within same meal)
              jsonbIngredientOccurrences.set(
                ingredientName,
                jsonbIngredientOccurrences.get(ingredientName)! + 1
              );

              // Store sample contexts (original ingredient text) - limit to avoid excessive data
              const contexts = jsonbIngredientContexts.get(ingredientName)!;
              if (contexts.length < 5) {
                // Reduced to 5 sample contexts for better performance
                contexts.push({
                  originalText: originalText,
                  mealId: meal.id,
                  mealName: meal.name,
                });
              }
            }
          });
        });

        // Include JSONB ingredients that don't already exist in master table
        const badIngredients: BadIngredient[] = [];

        for (const [ingredientName, usedInMeals] of Array.from(
          jsonbIngredientUsage.entries()
        )) {
          // Check if this JSONB ingredient name exists in master ingredients
          const masterMatch = masterIngredientsMap.get(
            ingredientName.toLowerCase()
          );

          // Check if this ingredient is currently in review
          const isInReview = reviewIngredientsSet.has(
            ingredientName.toLowerCase()
          );

          // Only include ingredients that DON'T have exact matches AND are NOT in review
          if (!masterMatch && !isInReview) {
            const contexts = jsonbIngredientContexts.get(ingredientName) || [];
            const totalOccurrences =
              jsonbIngredientOccurrences.get(ingredientName) || 0;
            badIngredients.push({
              name: ingredientName,
              usedInMeals: usedInMeals,
              sampleContexts: contexts,
              totalOccurrences: totalOccurrences,
              masterIngredientMatch: undefined, // No match since we filtered them out
            });
          }
        }

        // Apply search filter
        let filteredBadIngredients = badIngredients;
        if (debouncedSearch.trim()) {
          const searchTerm = debouncedSearch.trim().toLowerCase();

          // Simple substring search
          filteredBadIngredients = badIngredients.filter((ingredient) => {
            const name = ingredient.name.toLowerCase();
            return name.includes(searchTerm);
          });
        }

        // Apply sorting
        // Sort by usage count (most used first) - process high-impact items first
        filteredBadIngredients.sort(
          (a, b) => b.usedInMeals.length - a.usedInMeals.length
        );

        // Apply pagination
        const from = (currentPage - 1) * pageSize;
        const to = from + pageSize;
        const paginatedBadIngredients = filteredBadIngredients.slice(from, to);

        return {
          ingredients: paginatedBadIngredients,
          totalCount: filteredBadIngredients.length,
        };
      } catch (error) {
        console.error('Error in bad ingredients query:', error);
        return {
          ingredients: [],
          totalCount: 0,
        };
      }
    },
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes - longer for performance
  });

  const badIngredients = useMemo(
    () => badIngredientsData?.ingredients || [],
    [badIngredientsData?.ingredients]
  );
  const totalBadCount = badIngredientsData?.totalCount || 0;
  const totalPages = Math.ceil(totalBadCount / pageSize);

  // Memoize pattern grouping for performance
  const { ingredientsByPattern, ungroupedIngredients } = useMemo(() => {
    const patterns = new Map<string, BadIngredient[]>();
    const ungrouped: BadIngredient[] = [];

    badIngredients.forEach((ingredient) => {
      const pattern = detectIngredientPattern(ingredient.name);
      if (pattern) {
        if (!patterns.has(pattern)) {
          patterns.set(pattern, []);
        }
        patterns.get(pattern)!.push(ingredient);
      } else {
        ungrouped.push(ingredient);
      }
    });

    return { ingredientsByPattern: patterns, ungroupedIngredients: ungrouped };
  }, [badIngredients]);

  // Fetch all active ingredients for fuzzy matching
  const { data: allIngredients = [], refetch: refetchAllIngredients } =
    useQuery({
      queryKey: ['all-ingredients-for-matching'],
      queryFn: async () => {
        // Get ingredients with their meal counts
        const { data: ingredients, error: ingredientsError } = await supabase
          .from('ingredients')
          .select('id, name, emoji')
          .order('name', { ascending: true });

        if (ingredientsError) throw ingredientsError;

        // Get meal counts for each ingredient
        const { data: mealCounts, error: countsError } = await supabase
          .from('meal_ingredients')
          .select('ingredient_id');

        if (countsError) throw countsError;

        // Count occurrences of each ingredient
        const countMap = new Map();
        mealCounts.forEach((mc) => {
          countMap.set(
            mc.ingredient_id,
            (countMap.get(mc.ingredient_id) || 0) + 1
          );
        });

        // Include all ingredients, with meal counts (including 0 for unused ingredients)
        const allIngredientsWithCounts = ingredients.map((ingredient) => ({
          id: ingredient.id,
          name: ingredient.name,
          emoji: ingredient.emoji,
          meal_count: countMap.get(ingredient.id) || 0,
        }));

        return allIngredientsWithCounts;
      },
      staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    });

  // Enhanced fuzzy matching with better preprocessing and scoring
  const ingredientMatches = useMemo(() => {
    const matches = new Map<
      string,
      { id: string; name: string; emoji: string | null; similarity: number }[]
    >();

    if (allIngredients.length > 0 && badIngredients.length > 0) {
      // Create multiple fuse instances for different matching strategies
      const exactFuse = new Fuse(allIngredients, {
        keys: ['name'],
        threshold: 0.0, // Exact matches
        includeScore: true,
        ignoreLocation: true,
        minMatchCharLength: 1,
      });

      const fuzzyFuse = new Fuse(allIngredients, {
        keys: ['name'],
        threshold: 0.4, // More permissive threshold
        includeScore: true,
        ignoreLocation: true,
        minMatchCharLength: 2,
        distance: 100,
      });

      badIngredients.forEach((badIngredient) => {
        const originalName = badIngredient.name.toLowerCase().trim();

        // Preprocessing: normalize the ingredient name
        const cleanName = originalName
          .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();

        let allResults = new Set<string>(); // Track IDs to avoid duplicates
        let matchResults: {
          id: string;
          name: string;
          emoji: string | null;
          similarity: number;
        }[] = [];

        // Strategy 1: Try exact/near-exact matches first
        const exactResults = exactFuse.search(originalName);
        exactResults.forEach((result) => {
          if (!allResults.has(result.item.id)) {
            allResults.add(result.item.id);
            matchResults.push({
              id: result.item.id,
              name: result.item.name,
              emoji: result.item.emoji,
              similarity: 1 - (result.score || 0),
            });
          }
        });

        // Strategy 2: Try cleaned name if we don't have enough matches
        if (matchResults.length < 3 && cleanName !== originalName) {
          const cleanResults = exactFuse.search(cleanName);
          cleanResults.forEach((result) => {
            if (!allResults.has(result.item.id)) {
              allResults.add(result.item.id);
              matchResults.push({
                id: result.item.id,
                name: result.item.name,
                emoji: result.item.emoji,
                similarity: 1 - (result.score || 0),
              });
            }
          });
        }

        // Strategy 3: Fuzzy matching if still not enough
        if (matchResults.length < 3) {
          const fuzzyResults = fuzzyFuse.search(originalName);
          fuzzyResults.forEach((result) => {
            if (!allResults.has(result.item.id)) {
              allResults.add(result.item.id);
              matchResults.push({
                id: result.item.id,
                name: result.item.name,
                emoji: result.item.emoji,
                similarity: 1 - (result.score || 0),
              });
            }
          });
        }

        // Strategy 4: Try removing common preparation words
        if (matchResults.length < 3) {
          const prepWords = [
            'chopped',
            'diced',
            'sliced',
            'minced',
            'fresh',
            'dried',
            'ground',
            'whole',
            'crushed',
            'grated',
            'shredded',
            'cubed',
            'halved',
            'quartered',
            'finely',
            'coarsely',
            'roughly',
          ];
          const withoutPrep = cleanName
            .split(' ')
            .filter((word) => !prepWords.includes(word))
            .join(' ')
            .trim();

          if (withoutPrep && withoutPrep !== cleanName) {
            const prepResults = fuzzyFuse.search(withoutPrep);
            prepResults.forEach((result) => {
              if (!allResults.has(result.item.id)) {
                allResults.add(result.item.id);
                matchResults.push({
                  id: result.item.id,
                  name: result.item.name,
                  emoji: result.item.emoji,
                  similarity: (1 - (result.score || 0)) * 0.9, // High confidence for prep-removed matches
                });
              }
            });
          }
        }

        // Strategy 5: Try key ingredient words (but preserve multi-word ingredients)
        if (matchResults.length < 3 && cleanName.includes(' ')) {
          // First try common multi-word patterns
          const words = cleanName.split(' ');
          if (words.length >= 2) {
            // Try two-word combinations (e.g., "red onion" from "chopped red onion")
            for (let i = 0; i < words.length - 1; i++) {
              if (matchResults.length < 3) {
                const twoWord = words[i] + ' ' + words[i + 1];
                const twoWordResults = fuzzyFuse.search(twoWord);
                twoWordResults.slice(0, 1).forEach((result) => {
                  if (!allResults.has(result.item.id)) {
                    allResults.add(result.item.id);
                    matchResults.push({
                      id: result.item.id,
                      name: result.item.name,
                      emoji: result.item.emoji,
                      similarity: (1 - (result.score || 0)) * 0.85, // Good confidence for two-word matches
                    });
                  }
                });
              }
            }
          }

          // Then try individual meaningful words
          const meaningfulWords = words.filter(
            (word) => word.length > 2 && !['and', 'the', 'with'].includes(word)
          );
          meaningfulWords.forEach((word) => {
            if (matchResults.length < 3) {
              const wordResults = fuzzyFuse.search(word);
              wordResults.slice(0, 1).forEach((result) => {
                if (!allResults.has(result.item.id)) {
                  allResults.add(result.item.id);
                  matchResults.push({
                    id: result.item.id,
                    name: result.item.name,
                    emoji: result.item.emoji,
                    similarity: (1 - (result.score || 0)) * 0.6, // Lower confidence for single word matches
                  });
                }
              });
            }
          });
        }

        // Sort by similarity and take top 3
        matchResults.sort((a, b) => b.similarity - a.similarity);
        matches.set(badIngredient.name, matchResults.slice(0, 3));
      });
    }
    return matches;
  }, [allIngredients, badIngredients]);

  // Filtered ingredients are now handled by the query
  const filteredIngredients = badIngredients;

  // Memoized bulk selection handlers for better performance
  const handleSelectIngredient = useCallback(
    (ingredientName: string, selected: boolean) => {
      const newSelected = new Set(selectedIngredients);
      if (selected) {
        newSelected.add(ingredientName);
      } else {
        newSelected.delete(ingredientName);
      }
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    },
    [selectedIngredients]
  );

  const handleSelectAll = useCallback(
    (ingredients: BadIngredient[], selected: boolean) => {
      const newSelected = new Set(selectedIngredients);
      ingredients.forEach((ingredient) => {
        if (selected) {
          newSelected.add(ingredient.name);
        } else {
          newSelected.delete(ingredient.name);
        }
      });
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    },
    [selectedIngredients]
  );

  const handleBulkReview = async () => {
    // Mark all selected ingredients for review
    const ingredientNames = Array.from(selectedIngredients);

    try {
      // Process all ingredients in parallel
      await Promise.all(
        ingredientNames.map(async (ingredientName) => {
          const response = await fetch('/api/ingredients-for-review', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ingredientName }),
          });

          if (!response.ok) {
            throw new Error(`Failed to mark "${ingredientName}" for review`);
          }

          return response.json();
        })
      );

      // Clear selections and refresh the list
      setSelectedIngredients(new Set());
      setShowBulkActions(false);
      refetch();
    } catch (error) {
      console.error('Bulk review failed:', error);
      alert(
        `Bulk review failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  const handleBulkMerge = () => {
    setShowBulkMergeModal(true);
  };

  const executeBulkMerge = async () => {
    if (!selectedMasterIngredient) return;

    setIsBulkMerging(true);

    try {
      // Get all selected ingredients and their meal data
      const selectedIngredientsData = filteredIngredients.filter((ingredient) =>
        selectedIngredients.has(ingredient.name)
      );

      console.log(
        `Starting bulk merge of ${selectedIngredientsData.length} ingredients to "${selectedMasterIngredient.name}"`
      );

      // Process each ingredient immediately
      for (let i = 0; i < selectedIngredientsData.length; i++) {
        const ingredient = selectedIngredientsData[i];

        try {
          console.log(
            `Processing ${i + 1}/${selectedIngredientsData.length}: "${
              ingredient.name
            }" → "${selectedMasterIngredient.name}"`
          );

          // Get ALL meals that contain this ingredient
          const { data: allMeals, error: mealsError } = await supabase
            .from('meals')
            .select('id, name, ingredients')
            .not('ingredients', 'is', null);

          if (mealsError) throw mealsError;

          // Filter meals that actually contain the ingredient
          const mealsToUpdate =
            allMeals?.filter((meal) => {
              if (!meal.ingredients || !Array.isArray(meal.ingredients))
                return false;
              return meal.ingredients.some(
                (ing: any) => ing.name === ingredient.name
              );
            }) || [];

          console.log(
            `Found ${mealsToUpdate.length} meals containing "${ingredient.name}"`
          );

          // Update each meal
          for (const meal of mealsToUpdate) {
            if (!meal.ingredients || !Array.isArray(meal.ingredients)) continue;

            let hasChanges = false;
            const updatedIngredients = meal.ingredients.map((ing: any) => {
              if (ing.name === ingredient.name) {
                hasChanges = true;
                return {
                  ...ing,
                  name: selectedMasterIngredient.name,
                  ingredient_id: selectedMasterIngredient.id,
                };
              }
              return ing;
            });

            // Update the meal if changes were made
            if (hasChanges) {
              const { error: updateError } = await supabase
                .from('meals')
                .update({ ingredients: updatedIngredients })
                .eq('id', meal.id);

              if (updateError) {
                console.error(`Error updating meal ${meal.name}:`, updateError);
              }
            }
          }

          // Progress tracking removed
        } catch (error) {
          console.error(
            `Error processing ingredient "${ingredient.name}":`,
            error
          );
          // Continue with next ingredient even if one fails
        }
      }

      console.log('✅ Bulk merge completed successfully');

      // Mark selected ingredients as processed
      selectedIngredientsData.forEach((ingredient) => {
        setProcessedIngredients(
          (prev) => new Set([...Array.from(prev), ingredient.name])
        );
      });

      // Close modal and clear selections
      setShowBulkMergeModal(false);
      setSelectedIngredients(new Set());
      setShowBulkActions(false);
      setBulkMergeSearch('');
      setSelectedMasterIngredient(null);

      // Refresh data
      refetch();
      refetchAllIngredients();
    } catch (error) {
      console.error('❌ Bulk merge failed:', error);
      alert(
        `Bulk merge failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsBulkMerging(false);
    }
  };

  const handleCreateNewAndMerge = async () => {
    if (!bulkMergeSearch.trim()) return;

    setIsBulkMerging(true);

    try {
      // Create new master ingredient
      const { data: newIngredient, error } = await supabase
        .from('ingredients')
        .insert({
          name: bulkMergeSearch.trim(),
          emoji: '🥗',
          category: null,
        })
        .select()
        .single();

      if (error) throw error;

      console.log(
        `Created new ingredient: "${newIngredient.name}" (ID: ${newIngredient.id})`
      );

      // Get all selected ingredients and their meal data
      const selectedIngredientsData = filteredIngredients.filter((ingredient) =>
        selectedIngredients.has(ingredient.name)
      );

      console.log(
        `Starting bulk merge of ${selectedIngredientsData.length} ingredients to newly created "${newIngredient.name}"`
      );

      // Process each ingredient immediately
      for (let i = 0; i < selectedIngredientsData.length; i++) {
        const ingredient = selectedIngredientsData[i];

        try {
          console.log(
            `Processing ${i + 1}/${selectedIngredientsData.length}: "${
              ingredient.name
            }" → "${newIngredient.name}"`
          );

          // Get ALL meals that contain this ingredient
          const { data: allMeals, error: mealsError } = await supabase
            .from('meals')
            .select('id, name, ingredients')
            .not('ingredients', 'is', null);

          if (mealsError) throw mealsError;

          // Filter meals that actually contain the ingredient
          const mealsToUpdate =
            allMeals?.filter((meal) => {
              if (!meal.ingredients || !Array.isArray(meal.ingredients))
                return false;
              return meal.ingredients.some(
                (ing: any) => ing.name === ingredient.name
              );
            }) || [];

          console.log(
            `Found ${mealsToUpdate.length} meals containing "${ingredient.name}"`
          );

          // Update each meal
          for (const meal of mealsToUpdate) {
            if (!meal.ingredients || !Array.isArray(meal.ingredients)) continue;

            let hasChanges = false;
            const updatedIngredients = meal.ingredients.map((ing: any) => {
              if (ing.name === ingredient.name) {
                hasChanges = true;
                return {
                  ...ing,
                  name: newIngredient.name,
                  ingredient_id: newIngredient.id,
                };
              }
              return ing;
            });

            // Update the meal if changes were made
            if (hasChanges) {
              const { error: updateError } = await supabase
                .from('meals')
                .update({ ingredients: updatedIngredients })
                .eq('id', meal.id);

              if (updateError) {
                console.error(`Error updating meal ${meal.name}:`, updateError);
              }
            }
          }

          // Progress tracking removed
        } catch (error) {
          console.error(
            `Error processing ingredient "${ingredient.name}":`,
            error
          );
          // Continue with next ingredient even if one fails
        }
      }

      console.log('✅ Create and bulk merge completed successfully');

      // Mark selected ingredients as processed
      selectedIngredientsData.forEach((ingredient) => {
        setProcessedIngredients(
          (prev) => new Set([...Array.from(prev), ingredient.name])
        );
      });

      // Refresh ingredients lists
      refetchAllIngredients();

      // Close modal and clear selections
      setShowBulkMergeModal(false);
      setSelectedIngredients(new Set());
      setShowBulkActions(false);
      setBulkMergeSearch('');
      setSelectedMasterIngredient(null);

      // Refresh data
      refetch();
    } catch (error) {
      console.error('❌ Create and bulk merge failed:', error);
      alert(
        `Failed to create ingredient and merge: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsBulkMerging(false);
    }
  };

  // Handle fast actions
  const handleMerge = (
    fromName: string,
    toId: string,
    toName: string,
    meals: { id: string; name: string }[]
  ) => {
    addMergeOperation(fromName, toId, toName, meals);
    // Remove from selected ingredients if it was selected
    if (selectedIngredients.has(fromName)) {
      const newSelected = new Set(selectedIngredients);
      newSelected.delete(fromName);
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    }
  };

  const handleReview = (ingredientName: string) => {
    // Add to processed set immediately for UI feedback
    setProcessedIngredients(
      (prev) => new Set([...Array.from(prev), ingredientName])
    );
    // Remove from selected ingredients if it was selected
    if (selectedIngredients.has(ingredientName)) {
      const newSelected = new Set(selectedIngredients);
      newSelected.delete(ingredientName);
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    }
    // Mark ingredient for review
    markIngredientForReview.mutate(ingredientName);
  };

  const handleSkip = async (ingredientName: string) => {
    // Remove from selected ingredients if it was selected
    if (selectedIngredients.has(ingredientName)) {
      const newSelected = new Set(selectedIngredients);
      newSelected.delete(ingredientName);
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    }

    try {
      // Create the ingredient in the master ingredients table since it's correct as-is
      const { error: insertError } = await supabase.from('ingredients').insert({
        name: ingredientName,
        emoji: '🥗', // Default emoji
        category: null,
      });

      if (insertError && insertError.code !== '23505') {
        // Ignore duplicate key error
        console.error('Error creating ingredient:', insertError);
      }

      // Get the ingredient ID (whether newly created or existing)
      const { data: ingredient, error: selectError } = await supabase
        .from('ingredients')
        .select('id')
        .eq('name', ingredientName)
        .single();

      if (selectError) {
        console.error('Error getting ingredient ID:', selectError);
        throw selectError;
      }

      // Update all meals that contain this ingredient to add ingredient_id
      const { data: mealsWithIngredient, error: mealsError } = await supabase
        .from('meals')
        .select('id, ingredients')
        .not('ingredients', 'is', null);

      if (mealsError) {
        console.error('Error fetching meals:', mealsError);
        throw mealsError;
      }

      // Process each meal to add ingredient_id to matching ingredients
      for (const meal of mealsWithIngredient) {
        if (!meal.ingredients || !Array.isArray(meal.ingredients)) continue;

        let hasChanges = false;
        const updatedIngredients = meal.ingredients.map((ing: any) => {
          if (ing.name === ingredientName && !ing.ingredient_id) {
            hasChanges = true;
            return {
              ...ing,
              ingredient_id: ingredient.id,
            };
          }
          return ing;
        });

        // Update the meal if changes were made
        if (hasChanges) {
          const { error: updateError } = await supabase
            .from('meals')
            .update({ ingredients: updatedIngredients })
            .eq('id', meal.id);

          if (updateError) {
            console.error(`Error updating meal ${meal.id}:`, updateError);
          }
        }
      }

      // Add to processed set to hide from view
      setProcessedIngredients(
        (prev) => new Set([...Array.from(prev), ingredientName])
      );

      // Refresh both the master ingredients list and bad ingredients list
      refetchAllIngredients();
      refetch(); // This will re-query bad ingredients and filter out the newly added ingredient
    } catch (error) {
      console.error('Error in handleSkip:', error);
      // Still mark as processed to avoid blocking the user
      setProcessedIngredients(
        (prev) => new Set([...Array.from(prev), ingredientName])
      );
      // Refresh lists even if creation failed
      refetchAllIngredients();
      refetch();
    }
  };

  const handleSplit = async (
    fromName: string,
    splitInto: string[],
    meals: { id: string; name: string }[]
  ) => {
    // Remove from selected ingredients if it was selected
    if (selectedIngredients.has(fromName)) {
      const newSelected = new Set(selectedIngredients);
      newSelected.delete(fromName);
      setSelectedIngredients(newSelected);
      setShowBulkActions(newSelected.size > 0);
    }

    try {
      // First, ensure all split ingredients exist in the master table and get their IDs
      const ingredientIds: Record<string, string> = {};

      for (const ingredientName of splitInto) {
        const trimmedName = ingredientName.trim();

        // Try to insert, but if it already exists, get the existing one
        const { error: insertError } = await supabase
          .from('ingredients')
          .insert({
            name: trimmedName,
            emoji: '🥗', // Default emoji
            category: null,
          });

        // Whether insert succeeded or failed due to duplicate, get the ingredient ID
        const { data: existingIngredient, error: selectError } = await supabase
          .from('ingredients')
          .select('id')
          .eq('name', trimmedName)
          .single();

        if (selectError) {
          console.error(
            `Error getting ingredient ID for "${trimmedName}":`,
            selectError
          );
          throw selectError;
        }

        ingredientIds[trimmedName] = existingIngredient.id;

        if (insertError && insertError.code !== '23505') {
          // Log non-duplicate errors
          console.error(
            `Error creating ingredient "${trimmedName}":`,
            insertError
          );
        }
      }

      // Get all meals that contain this ingredient and update their JSONB
      const { data: mealsToUpdate, error: mealsError } = await supabase
        .from('meals')
        .select('id, name, ingredients')
        .in(
          'id',
          meals.map((m) => m.id)
        );

      if (mealsError) {
        throw mealsError;
      }

      // Process each meal to split the ingredient
      for (const meal of mealsToUpdate) {
        if (!meal.ingredients || !Array.isArray(meal.ingredients)) continue;

        let hasChanges = false;
        const updatedIngredients = [...meal.ingredients];

        // Find the ingredient to split and replace it with multiple ingredients
        const ingredientIndex = updatedIngredients.findIndex(
          (ing: any) => ing.name === fromName
        );

        if (ingredientIndex !== -1) {
          const originalIngredient = updatedIngredients[ingredientIndex];
          hasChanges = true;

          // Remove the original compound ingredient
          updatedIngredients.splice(ingredientIndex, 1);

          // Add the split ingredients with master ingredient IDs
          splitInto.forEach((splitName) => {
            const trimmedName = splitName.trim();
            updatedIngredients.push({
              ...originalIngredient, // Preserve quantity, units, etc.
              name: trimmedName,
              ingredient_id: ingredientIds[trimmedName], // Add ingredient reference
              // Note: This distributes the original quantity to each ingredient
              // You might want to adjust this logic based on your needs
            });
          });
        }

        // Update the meal if changes were made
        if (hasChanges) {
          const { error: updateError } = await supabase
            .from('meals')
            .update({ ingredients: updatedIngredients })
            .eq('id', meal.id);

          if (updateError) {
            console.error(`Error updating meal ${meal.id}:`, updateError);
            throw updateError;
          }
        }
      }

      // Mark as processed and refresh
      setProcessedIngredients(
        (prev) => new Set([...Array.from(prev), fromName])
      );
      refetchAllIngredients();
      refetch();
    } catch (error) {
      console.error('Error in handleSplit:', error);
      alert(
        `Failed to split ingredient: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  // Add merge operation
  const addMergeOperation = (
    fromIngredientName: string,
    toIngredientId: string,
    toIngredientName: string,
    affectedMeals: { id: string; name: string }[]
  ) => {
    setMergeOperations((prev) => [
      ...prev,
      { fromIngredientName, toIngredientId, toIngredientName, affectedMeals },
    ]);
  };

  // Remove merge operation
  const removeMergeOperation = (index: number) => {
    setMergeOperations((prev) => prev.filter((_, i) => i !== index));
  };

  // Execute merge operations
  const executeMergeOperations = useMutation({
    mutationFn: async () => {
      for (let i = 0; i < mergeOperations.length; i++) {
        const operation = mergeOperations[i];

        try {
          console.log(
            `Processing merge ${i + 1}/${mergeOperations.length}: "${
              operation.fromIngredientName
            }" → "${operation.toIngredientName}"`
          );

          // Update JSONB ingredients in meals table
          // Get only the specific meals that we know contain this ingredient
          const mealIds = operation.affectedMeals.map((meal) => meal.id);
          console.log(
            'Expected affected meals:',
            operation.affectedMeals.map((m) => `${m.id} (${m.name})`)
          );

          // Get ALL meals that contain this ingredient (don't rely on pre-queued IDs)
          const { data: allMeals, error: mealsError } = await supabase
            .from('meals')
            .select('id, name, ingredients')
            .not('ingredients', 'is', null);

          // Filter meals that actually contain the ingredient we're looking for
          const mealsToUpdate =
            allMeals?.filter((meal) => {
              if (!meal.ingredients || !Array.isArray(meal.ingredients))
                return false;
              return meal.ingredients.some(
                (ing: any) => ing.name === operation.fromIngredientName
              );
            }) || [];

          console.log(
            `Processing "${operation.fromIngredientName}" → "${operation.toIngredientName}" in ${mealsToUpdate.length} meals`
          );

          if (mealsError) {
            console.error('Error fetching meals:', mealsError);
            throw mealsError;
          }

          let updatedMealsCount = 0;

          // Process meals in smaller batches to avoid timeouts
          const batchSize = 5;
          for (
            let batchStart = 0;
            batchStart < mealsToUpdate.length;
            batchStart += batchSize
          ) {
            const batch = mealsToUpdate.slice(
              batchStart,
              batchStart + batchSize
            );
            console.log(
              `Processing batch ${
                Math.floor(batchStart / batchSize) + 1
              }/${Math.ceil(mealsToUpdate.length / batchSize)}`
            );

            for (const meal of batch) {
              if (!meal.ingredients || !Array.isArray(meal.ingredients))
                continue;

              let hasChanges = false;
              const updatedIngredients = meal.ingredients.map(
                (ingredient: any) => {
                  if (ingredient.name === operation.fromIngredientName) {
                    hasChanges = true;
                    return {
                      ...ingredient,
                      name: operation.toIngredientName,
                      ingredient_id: operation.toIngredientId,
                    };
                  }
                  return ingredient;
                }
              );

              // Update the meal if changes were made
              if (hasChanges) {
                const { error: updateError } = await supabase
                  .from('meals')
                  .update({ ingredients: updatedIngredients })
                  .eq('id', meal.id);

                if (updateError) {
                  console.error(
                    `❌ Error updating meal ${meal.name}:`,
                    updateError
                  );
                } else {
                  updatedMealsCount++;
                }
              }
            }

            // Small delay between batches
            await new Promise((resolve) => setTimeout(resolve, 100));
          }

          // Progress tracking removed
        } catch (error) {
          console.error('Error processing merge operation:', error);
          // Processing complete
          throw error;
        }
      }

      // Processing complete
      setMergeOperations([]);
      refetch(); // Refresh the orphaned ingredients list
      refetchAllIngredients(); // Refresh the active ingredients list
    },
    onSuccess: () => {
      console.log('✅ Merge operations completed successfully');
      // The refetch() call in the mutation function will handle refreshing the data
    },
    onError: (error) => {
      console.error('❌ Error executing merge operations:', error);
      // Processing complete
    },
  });

  // Sync all JSONB ingredient names with their master ingredients
  const syncIngredientNames = useMutation({
    mutationFn: async () => {
      // Get all master ingredients for lookup
      const { data: masterIngredients, error: masterError } = await supabase
        .from('ingredients')
        .select('id, name, emoji');

      if (masterError) {
        throw masterError;
      }

      // Create lookup map for master ingredients
      const masterIngredientsMap = new Map();
      masterIngredients.forEach((ingredient) => {
        masterIngredientsMap.set(ingredient.id, ingredient);
      });

      console.log(`Found ${masterIngredients.length} master ingredients`);

      // Get all meals with JSONB ingredients
      const { data: meals, error: mealsError } = await supabase
        .from('meals')
        .select('id, name, ingredients')
        .not('ingredients', 'is', null);

      if (mealsError) {
        console.error('Error fetching meals:', mealsError);
        throw mealsError;
      }

      console.log(
        `Processing ${meals.length} meals for ingredient name sync...`
      );

      let updatedMealsCount = 0;
      let syncedIngredientsCount = 0;

      // Process each meal
      for (const meal of meals) {
        if (!meal.ingredients || !Array.isArray(meal.ingredients)) continue;

        let mealHasChanges = false;
        const updatedIngredients = meal.ingredients.map((ingredient: any) => {
          // Check if ingredient has ingredient_id
          if (ingredient.ingredient_id) {
            const masterIngredient = masterIngredientsMap.get(
              ingredient.ingredient_id
            );

            if (masterIngredient) {
              // Check if name needs updating
              if (ingredient.name !== masterIngredient.name) {
                console.log(
                  `Syncing "${ingredient.name}" → "${masterIngredient.name}" in meal "${meal.name}"`
                );
                mealHasChanges = true;
                syncedIngredientsCount++;

                return {
                  ...ingredient,
                  name: masterIngredient.name,
                  emoji: masterIngredient.emoji || ingredient.emoji, // Update emoji too
                };
              }
            } else {
              console.warn(
                `Master ingredient ID ${ingredient.ingredient_id} not found for ingredient "${ingredient.name}" in meal "${meal.name}"`
              );
            }
          }

          return ingredient;
        });

        // Update meal if changes were made
        if (mealHasChanges) {
          const response = await fetch(`/api/meals/${meal.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ingredients: updatedIngredients }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error(`Error updating meal "${meal.name}":`, errorData);
            throw new Error(
              `Failed to update meal "${meal.name}": ${
                errorData.error || 'Update failed'
              }`
            );
          }

          updatedMealsCount++;
        }
      }

      console.log(
        `Sync complete! Updated ${syncedIngredientsCount} ingredients across ${updatedMealsCount} meals`
      );

      return {
        syncedIngredientsCount,
        updatedMealsCount,
        totalMealsProcessed: meals.length,
      };
    },
    onSuccess: (result) => {
      alert(
        `Sync complete!\n\n- Synced ${result.syncedIngredientsCount} ingredients\n- Updated ${result.updatedMealsCount} meals\n- Processed ${result.totalMealsProcessed} total meals`
      );
      refetch(); // Refresh the bad ingredients list
    },
    onError: (error) => {
      console.error('Sync failed:', error);
      alert(`Sync failed: ${error.message}`);
    },
  });

  // Mark ingredient for review
  const markIngredientForReview = useMutation({
    mutationFn: async (ingredientName: string) => {
      const response = await fetch('/api/ingredients-for-review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ingredientName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Failed to mark ingredient for review: ${errorData.error}`
        );
      }

      return await response.json();
    },
    onSuccess: (data, ingredientName) => {
      console.log(`Marked "${ingredientName}" for review`);
      // Refresh the bad ingredients list to remove the processed item
      refetch();
    },
    onError: (error, ingredientName) => {
      console.error(`Failed to mark "${ingredientName}" for review:`, error);
      alert(`Failed to mark ingredient for review: ${error.message}`);
    },
  });

  // Helper function to remove item from review
  const removeFromReview = async (ingredientName: string) => {
    try {
      const response = await fetch(
        `/api/ingredients-for-review?ingredient=${encodeURIComponent(
          ingredientName
        )}`,
        {
          method: 'DELETE',
        }
      );
      if (response.ok) {
        refetchReviewIngredients();
      }
    } catch (error) {
      console.error('Error removing from review:', error);
    }
  };

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 overflow-auto">
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Bad Ingredients
                </h1>
                <p className="text-slate-400 text-base">
                  JSONB ingredient names that don&apos;t match master
                  ingredients - merge them to standardize your data
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <Button
                  onClick={() => syncIngredientNames.mutate()}
                  disabled={syncIngredientNames.isPending}
                  variant="outline"
                  className="bg-blue-700/50 border-blue-600 text-blue-300 hover:bg-blue-600/50 disabled:opacity-50"
                >
                  {syncIngredientNames.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Syncing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Sync Ingredient Names
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </div>
            </div>

            {/* Stats and Controls */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="card-dark p-6">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-8 w-8 text-red-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {totalBadCount}
                    </div>
                    <div className="text-sm text-slate-400">
                      Bad JSONB Ingredients
                    </div>
                  </div>
                </div>
              </div>

              <div className="card-dark p-6">
                <div className="flex items-center space-x-3">
                  <Check className="h-8 w-8 text-green-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {processedIngredients.size}
                    </div>
                    <div className="text-sm text-slate-400">Processed</div>
                  </div>
                </div>
              </div>

              <div className="card-dark p-6">
                <div className="flex items-center space-x-3">
                  <Merge className="h-8 w-8 text-blue-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {mergeOperations.length}
                    </div>
                    <div className="text-sm text-slate-400">Queued Merges</div>
                  </div>
                </div>
              </div>

              <div className="card-dark p-6">
                <div className="flex items-center space-x-3">
                  <FileText className="h-8 w-8 text-purple-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {allIngredients.length}
                    </div>
                    <div className="text-sm text-slate-400">
                      Master Ingredients
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Search */}
            <div className="card-dark p-6 mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search ingredients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                />
              </div>

              {/* Bulk Selection Controls */}
              {selectedIngredients.size > 0 && (
                <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CheckSquare className="h-5 w-5 text-blue-400" />
                      <span className="text-blue-400 font-medium">
                        {selectedIngredients.size} ingredient
                        {selectedIngredients.size !== 1 ? 's' : ''} selected
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={handleBulkMerge}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Merge className="h-4 w-4 mr-1" />
                        Bulk Merge ({selectedIngredients.size})
                      </Button>
                      <Button
                        onClick={handleBulkReview}
                        size="sm"
                        className="bg-yellow-600 hover:bg-yellow-700 text-white"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Bulk Review ({selectedIngredients.size})
                      </Button>
                      <Button
                        onClick={() => {
                          setSelectedIngredients(new Set());
                          setShowBulkActions(false);
                        }}
                        variant="outline"
                        size="sm"
                        className="bg-slate-700/50 border-slate-600 text-slate-300"
                      >
                        Clear Selection
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Smart Suggestions for Search */}
              {debouncedSearch.trim() && (
                <div className="mt-4 p-4 bg-purple-900/20 border border-purple-500/30 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Target className="h-5 w-5 text-purple-400" />
                      <span className="text-purple-400 font-medium">
                        Search Results for &quot;{debouncedSearch}&quot; -
                        Select Similar Items
                      </span>
                    </div>
                    <Button
                      onClick={() => {
                        // Select all ingredients from the current filtered results
                        const matchingIngredients = badIngredients.filter(
                          (ingredient) =>
                            !processedIngredients.has(ingredient.name)
                        );
                        handleSelectAll(matchingIngredients, true);
                      }}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      <CheckSquare className="h-4 w-4 mr-1" />
                      Select All Matching
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Fast Processing Interface */}
            <div className="card-dark">
              <div className="p-6 border-b border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      Fast Ingredient Processing
                    </h3>
                    <p className="text-slate-400 text-sm mt-1">
                      Quick actions for bad JSONB ingredients - click suggested
                      matches or search for custom mappings
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-slate-400">
                    <span>
                      Page {currentPage} of {totalPages}
                    </span>
                  </div>
                </div>
              </div>

              {isLoading ? (
                <div className="p-8 text-center">
                  <Loader2 className="mx-auto h-8 w-8 text-slate-400 animate-spin" />
                  <p className="mt-2 text-slate-400">Loading ingredients...</p>
                </div>
              ) : filteredIngredients.filter(
                  (ing) => !processedIngredients.has(ing.name)
                ).length === 0 ? (
                <div className="p-8 text-center">
                  <Check className="mx-auto h-8 w-8 text-green-500" />
                  <p className="mt-2 text-green-400 font-medium">
                    All ingredients on this page have been processed!
                  </p>
                  <p className="text-slate-400 text-sm mt-1">
                    {totalPages > currentPage
                      ? 'Go to the next page to continue.'
                      : 'Great job! All ingredients are processed.'}
                  </p>
                  {totalPages > currentPage && (
                    <Button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      className="mt-4 bg-blue-600 hover:bg-blue-700"
                    >
                      Next Page
                    </Button>
                  )}
                </div>
              ) : (
                <div>
                  <div className="p-6 space-y-3">
                    {filteredIngredients
                      .filter(
                        (ingredient) =>
                          !processedIngredients.has(ingredient.name)
                      )
                      .map((ingredient) => {
                        const pattern = detectIngredientPattern(
                          ingredient.name
                        );
                        return (
                          <FastEditRow
                            key={ingredient.name}
                            badIngredient={ingredient}
                            onMerge={handleMerge}
                            onReview={handleReview}
                            onSkip={handleSkip}
                            onSplit={handleSplit}
                            allIngredients={allIngredients}
                            suggestedMatches={
                              ingredientMatches.get(ingredient.name) || []
                            }
                            isSelected={selectedIngredients.has(
                              ingredient.name
                            )}
                            onSelectionChange={(selected) =>
                              handleSelectIngredient(ingredient.name, selected)
                            }
                            patternGroup={pattern}
                          />
                        );
                      })}
                  </div>

                  {/* Pagination Controls */}
                  {totalPages > 1 && (
                    <div className="p-6 border-t border-slate-700">
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-slate-400">
                          Showing {(currentPage - 1) * pageSize + 1} to{' '}
                          {Math.min(currentPage * pageSize, totalBadCount)} of{' '}
                          {totalBadCount} bad ingredients
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            onClick={() => setCurrentPage(currentPage - 1)}
                            disabled={currentPage === 1}
                            variant="outline"
                            size="sm"
                            className="bg-slate-700/50 border-slate-600 text-slate-300 disabled:opacity-50"
                          >
                            Previous
                          </Button>

                          <div className="flex items-center space-x-1">
                            {Array.from(
                              { length: Math.min(5, totalPages) },
                              (_, i) => {
                                const pageNumber =
                                  Math.max(
                                    1,
                                    Math.min(totalPages - 4, currentPage - 2)
                                  ) + i;
                                if (pageNumber > totalPages) return null;

                                return (
                                  <Button
                                    key={pageNumber}
                                    onClick={() => setCurrentPage(pageNumber)}
                                    variant={
                                      currentPage === pageNumber
                                        ? 'default'
                                        : 'outline'
                                    }
                                    size="sm"
                                    className={
                                      currentPage === pageNumber
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-slate-700/50 border-slate-600 text-slate-300'
                                    }
                                  >
                                    {pageNumber}
                                  </Button>
                                );
                              }
                            )}
                          </div>

                          <Button
                            onClick={() => setCurrentPage(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            variant="outline"
                            size="sm"
                            className="bg-slate-700/50 border-slate-600 text-slate-300 disabled:opacity-50"
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Merge Operations Container */}
              {mergeOperations.length > 0 && (
                <div className="mt-6 bg-slate-900/95 backdrop-blur-sm border border-slate-700 rounded-lg">
                  <div className="max-w-7xl mx-auto">
                    {/* Operation List */}
                    <div className="p-4 border-b border-slate-700/50 max-h-48 overflow-y-auto">
                      <div className="space-y-2">
                        {mergeOperations.map((operation, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between bg-slate-800/50 border border-slate-700/50 rounded-lg px-3 py-2"
                          >
                            <div className="flex items-center space-x-3">
                              <Badge
                                variant="outline"
                                className="bg-orange-600/20 text-orange-400 border-orange-500/50 text-xs"
                              >
                                {operation.fromIngredientName} →{' '}
                                {operation.toIngredientName}
                              </Badge>
                              <span className="text-slate-400 text-xs">
                                {operation.affectedMeals.length} meal
                                {operation.affectedMeals.length !== 1
                                  ? 's'
                                  : ''}
                              </span>
                            </div>
                            <Button
                              onClick={() => removeMergeOperation(index)}
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 hover:bg-red-600/30 text-red-400"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Control Bar */}
                    <div className="p-4 flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Merge className="h-5 w-5 text-orange-400" />
                          <span className="text-white font-medium">
                            {mergeOperations.length} merge operation
                            {mergeOperations.length !== 1 ? 's' : ''} queued
                          </span>
                        </div>
                        <div className="text-slate-400 text-sm">
                          {mergeOperations.reduce(
                            (sum, op) => sum + op.affectedMeals.length,
                            0
                          )}{' '}
                          meals will be updated
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          onClick={() => setMergeOperations([])}
                          variant="outline"
                          size="sm"
                          className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
                        >
                          Clear All
                        </Button>
                        <Button
                          onClick={() => executeMergeOperations.mutate()}
                          disabled={executeMergeOperations.isPending}
                          className="bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-2 shadow-lg"
                        >
                          {executeMergeOperations.isPending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Zap className="mr-2 h-4 w-4" />
                              Execute All Merges
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Merge Modal */}
      {showBulkMergeModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 border border-slate-600 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
            {/* Fixed Header */}
            <div className="p-6 border-b border-slate-700 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-white">
                    Bulk Merge Ingredients
                  </h2>
                  <p className="text-slate-400 text-sm mt-1">
                    Merge {selectedIngredients.size} selected ingredients to a
                    master ingredient
                  </p>
                </div>
                <Button
                  onClick={() => {
                    setShowBulkMergeModal(false);
                    setBulkMergeSearch('');
                    setSelectedMasterIngredient(null);
                  }}
                  variant="ghost"
                  size="sm"
                  className="text-slate-400 hover:text-white"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="p-6 space-y-6 flex-1 overflow-y-auto">
              {/* Selected Ingredients Preview */}
              <div>
                <h3 className="text-sm font-medium text-white mb-3">
                  Selected Ingredients:
                </h3>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {Array.from(selectedIngredients).map((ingredientName) => (
                    <span
                      key={ingredientName}
                      className="inline-flex items-center bg-blue-900/30 border border-blue-500/30 rounded-full px-3 py-1.5 text-sm text-blue-300"
                    >
                      {ingredientName}
                    </span>
                  ))}
                </div>
              </div>

              {/* Search for Master Ingredient */}
              <div>
                <h3 className="text-sm font-medium text-white mb-3">
                  Search for Master Ingredient:
                </h3>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <input
                    type="text"
                    placeholder="Search existing ingredients or type new name..."
                    value={bulkMergeSearch}
                    onChange={(e) => setBulkMergeSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-slate-700/50 border border-slate-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Search Results */}
              {bulkMergeSearch.trim() && (
                <div>
                  <h3 className="text-sm font-medium text-white mb-3">
                    Search Results:
                    <span className="text-slate-400 text-xs ml-2">
                      (
                      {
                        allIngredients.filter((ingredient) =>
                          ingredient.name
                            .toLowerCase()
                            .includes(bulkMergeSearch.toLowerCase())
                        ).length
                      }{' '}
                      found)
                    </span>
                  </h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {(() => {
                      const filteredIngredients = allIngredients.filter(
                        (ingredient) =>
                          ingredient.name
                            .toLowerCase()
                            .includes(bulkMergeSearch.toLowerCase())
                      );

                      if (filteredIngredients.length === 0) {
                        return (
                          <div className="text-center py-4 text-slate-400">
                            No ingredients found matching &quot;
                            {bulkMergeSearch}&quot;
                          </div>
                        );
                      }

                      return filteredIngredients
                        .slice(0, 20)
                        .map((ingredient) => (
                          <button
                            key={ingredient.id}
                            onClick={() =>
                              setSelectedMasterIngredient(ingredient)
                            }
                            className={`w-full flex items-center justify-between px-3 py-2 rounded border transition-colors $\{
                            selectedMasterIngredient?.id === ingredient.id
                              ? 'border-blue-500 bg-blue-900/30 text-blue-300'
                              : 'border-slate-600 bg-slate-700/30 text-white hover:bg-slate-600/50'
                          \}`}
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-lg">
                                {ingredient.emoji || '🥗'}
                              </span>
                              <span>{ingredient.name}</span>
                            </div>
                            {selectedMasterIngredient?.id === ingredient.id && (
                              <Check className="h-4 w-4 text-blue-400" />
                            )}
                          </button>
                        ));
                    })()}

                    {/* Create New Ingredient Option */}
                    <button
                      onClick={() =>
                        setSelectedMasterIngredient({
                          id: 'new',
                          name: bulkMergeSearch.trim(),
                          emoji: '🥗',
                        })
                      }
                      className={`w-full flex items-center justify-between px-3 py-2 rounded border transition-colors $\{
                        selectedMasterIngredient?.id === 'new'
                          ? 'border-green-500 bg-green-900/30 text-green-300'
                          : 'border-slate-600 bg-slate-700/30 text-white hover:bg-slate-600/50'
                      \}`}
                    >
                      <div className="flex items-center space-x-3">
                        <Plus className="h-4 w-4 text-green-400" />
                        <span>
                          Create new: &quot;{bulkMergeSearch.trim()}&quot;
                        </span>
                      </div>
                      {selectedMasterIngredient?.id === 'new' && (
                        <Check className="h-4 w-4 text-green-400" />
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Selected Master Ingredient */}
              {selectedMasterIngredient && (
                <div className="p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">
                      {selectedMasterIngredient.emoji}
                    </span>
                    <div>
                      <div className="text-green-400 font-medium">
                        {selectedMasterIngredient.id === 'new'
                          ? 'Will create: '
                          : 'Selected: '}
                        {selectedMasterIngredient.name}
                      </div>
                      <div className="text-green-300 text-sm">
                        All {selectedIngredients.size} ingredients will be
                        merged to this
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Fixed Footer */}
            <div className="p-6 border-t border-slate-700 flex items-center justify-end space-x-3 flex-shrink-0">
              <Button
                onClick={() => {
                  setShowBulkMergeModal(false);
                  setBulkMergeSearch('');
                  setSelectedMasterIngredient(null);
                }}
                variant="outline"
                className="bg-slate-700/50 border-slate-600 text-slate-300"
              >
                Cancel
              </Button>
              <Button
                onClick={
                  selectedMasterIngredient?.id === 'new'
                    ? handleCreateNewAndMerge
                    : executeBulkMerge
                }
                disabled={!selectedMasterIngredient || isBulkMerging}
                className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
              >
                {isBulkMerging ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Merge className="h-4 w-4 mr-2" />
                    {selectedMasterIngredient?.id === 'new'
                      ? 'Create & Merge Now'
                      : 'Merge Now'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </AuthGuard>
  );
}

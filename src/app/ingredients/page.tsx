'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Pencil,
  Trash2,
  Search,
  Carrot,
  AlertTriangle,
  ExternalLink,
  Plus,
  Eye,
  UtensilsCrossed,
} from 'lucide-react';
import Link from 'next/link';
import { EmojiPicker } from '@/components/ui/emoji-picker';

interface Ingredient {
  id: string;
  name: string;
  emoji: string | null;
  category: string | null;
  needs_review?: boolean;
  created_at: string;
  updated_at: string;
  meals?: Array<{ id: string; name: string; isLegacy?: boolean }>;
}

interface PaginatedIngredientsResult {
  data: Ingredient[];
  count: number;
  totalPages: number;
}

export default function IngredientsPage() {
  const [searchInput, setSearchInput] = useState('');
  const [activeSearchTerm, setActiveSearchTerm] = useState('');
  const [showReviewOnly, setShowReviewOnly] = useState(false);
  const [expandedIngredients, setExpandedIngredients] = useState<Set<string>>(new Set());
  const [loadingMeals, setLoadingMeals] = useState<Set<string>>(new Set());
  const [ingredientMeals, setIngredientMeals] = useState<Map<string, Array<{ id: string; name: string }>>>(new Map());
  const [editingIngredient, setEditingIngredient] = useState<Ingredient | null>(
    null
  );
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const queryClient = useQueryClient();
  const itemsPerPage = 50;

  // Fetch ingredients with pagination - NO CACHING for immediate updates
  const { data: ingredientsData, isLoading, error: queryError, refetch } = useQuery({
    queryKey: ['ingredients', activeSearchTerm, currentPage, showReviewOnly],
    queryFn: async (): Promise<PaginatedIngredientsResult> => {
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage - 1;

      let query = supabase
        .from('ingredients')
        .select('*', { count: 'exact' })
        .order('name', { ascending: true })
        .range(startIndex, endIndex);

      if (activeSearchTerm) {
        query = query.ilike('name', `%${activeSearchTerm}%`);
      }

      if (showReviewOnly) {
        query = query.eq('needs_review', true);
      }

      const { data, error, count } = await query;
      if (error) throw error;

      // No meal relationships loaded initially for performance
      const ingredientsWithMeals = (data || []).map((ingredient) => ({
        ...ingredient,
        meals: [], // Load on-demand via View Meals button
      }));

      return {
        data: ingredientsWithMeals,
        count: count || 0,
        totalPages: Math.ceil((count || 0) / itemsPerPage),
      };
    },
    retry: 1, // Reduce retries for faster feedback
    staleTime: 0, // No caching - always fresh data
    gcTime: 0, // Don't cache results
    refetchOnWindowFocus: true, // Refetch when window gets focus
    refetchOnMount: true, // Always refetch on mount
  });

  const ingredients = ingredientsData?.data || [];
  const totalIngredients = ingredientsData?.count || 0;
  const totalPages = ingredientsData?.totalPages || 0;

  // Create ingredient mutation - immediate updates
  const createMutation = useMutation({
    mutationFn: async (
      ingredient: Omit<Ingredient, 'id' | 'created_at' | 'updated_at'>
    ) => {
      const { error } = await supabase.from('ingredients').insert({
        name: ingredient.name,
        emoji: ingredient.emoji,
        category: ingredient.category,
      });

      if (error) throw error;
    },
    onSuccess: () => {
      // Force immediate refresh
      queryClient.removeQueries({ queryKey: ['ingredients'] });
      refetch();
      setEditingIngredient(null);
      // Clear loaded meals since ingredients changed
      setIngredientMeals(new Map());
      setExpandedIngredients(new Set());
    },
    onError: (error: any) => {
      console.error('Failed to create ingredient:', error);
      alert(`Failed to create ingredient: ${error.message || 'Unknown error'}`);
    },
  });

  // Update ingredient mutation - immediate updates
  const updateMutation = useMutation({
    mutationFn: async (ingredient: Ingredient) => {
      const { error } = await supabase
        .from('ingredients')
        .update({
          name: ingredient.name,
          emoji: ingredient.emoji,
          category: ingredient.category,
        })
        .eq('id', ingredient.id);

      if (error) throw error;
    },
    onSuccess: () => {
      // Force immediate refresh of ingredients
      queryClient.removeQueries({ queryKey: ['ingredients'] });
      refetch();
      setEditingIngredient(null);
      // Clear loaded meals since ingredients changed
      setIngredientMeals(new Map());
      setExpandedIngredients(new Set());
      
      // IMPORTANT: Invalidate meal queries since ingredients cascade to meals
      queryClient.invalidateQueries({ queryKey: ['meal'] });
      queryClient.invalidateQueries({ queryKey: ['meals'] });
      
      console.log('Ingredient updated - invalidated meal caches for UI refresh');
    },
    onError: (error: any) => {
      console.error('Failed to update ingredient:', error);
      alert(`Failed to update ingredient: ${error.message || 'Unknown error'}`);
    },
  });

  // Delete ingredient mutation - immediate updates
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('ingredients')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      // Force immediate refresh
      queryClient.removeQueries({ queryKey: ['ingredients'] });
      refetch();
      setDeleteConfirmId(null);
      // Clear loaded meals since ingredients changed
      setIngredientMeals(new Map());
      setExpandedIngredients(new Set());
    },
  });

  const handleSave = () => {
    if (editingIngredient) {
      if (editingIngredient.id) {
        // Update existing ingredient
        updateMutation.mutate(editingIngredient);
      } else {
        // Create new ingredient
        createMutation.mutate({
          name: editingIngredient.name,
          emoji: editingIngredient.emoji,
          category: editingIngredient.category,
        });
      }
    }
  };

  const handleDelete = (id: string) => {
    deleteMutation.mutate(id);
  };

  const handleSearch = () => {
    setActiveSearchTerm(searchInput);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setActiveSearchTerm('');
    setCurrentPage(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Clear loaded meals when changing pages to free memory
    setIngredientMeals(new Map());
    setExpandedIngredients(new Set());
    setLoadingMeals(new Set());
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const toggleExpandedIngredient = (ingredientId: string) => {
    const newExpanded = new Set(expandedIngredients);
    if (newExpanded.has(ingredientId)) {
      newExpanded.delete(ingredientId);
    } else {
      newExpanded.add(ingredientId);
    }
    setExpandedIngredients(newExpanded);
  };

  const loadMealsForIngredient = async (ingredient: Ingredient) => {
    // Don't load if already loading or already loaded
    if (loadingMeals.has(ingredient.id) || ingredientMeals.has(ingredient.id)) {
      return;
    }

    // Set loading state
    setLoadingMeals(prev => new Set([...Array.from(prev), ingredient.id]));

    try {
      // Use direct SQL query with proper escaping - more reliable than RPC
      const { data: mealsData, error } = await supabase
        .from('meals')
        .select('id, name')
        .filter('ingredients', 'cs', JSON.stringify([{ ingredient_id: ingredient.id }]));

      if (error) {
        console.error('Supabase filter error for ingredient:', ingredient.name, error);
        throw error;
      }

      console.log(`Found ${mealsData?.length || 0} meals for ingredient: ${ingredient.name} (${ingredient.id})`);
      console.log('Meals data:', mealsData);

      // Store the results - use functional update to ensure proper state management
      setIngredientMeals(prev => {
        const newMap = new Map(prev);
        newMap.set(ingredient.id, mealsData || []);
        console.log(`Stored meals for ${ingredient.name}:`, mealsData);
        return newMap;
      });
    } catch (error) {
      console.error('Error loading meals for ingredient:', ingredient.name, error);
      // Store empty array on error so we don't keep trying
      setIngredientMeals(prev => {
        const newMap = new Map(prev);
        newMap.set(ingredient.id, []);
        return newMap;
      });
    } finally {
      // Remove loading state
      setLoadingMeals(prev => {
        const newSet = new Set(prev);
        newSet.delete(ingredient.id);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-slate-400">Loading ingredients...</p>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (queryError) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-32 w-32 text-red-500" />
              <p className="mt-4 text-red-400">Error loading ingredients</p>
              <p className="mt-2 text-slate-400 text-sm">{queryError.message}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 overflow-auto">
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Master Ingredients
                </h1>
                <p className="text-slate-400 text-base">
                  Manage your centralized ingredients database (
                  {totalIngredients} total ingredients)
                </p>
              </div>
              <Button
                onClick={() =>
                  setEditingIngredient({
                    id: '',
                    name: '',
                    emoji: '🥗',
                    category: null,
                    created_at: '',
                    updated_at: '',
                  })
                }
                className="bg-green-600 hover:bg-green-700 text-white border-0 transition-all shadow-lg shadow-green-600/20"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Ingredient
              </Button>
            </div>

            {/* Search */}
            <div className="mb-6">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Search ingredients..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-400"
                  />
                </div>
                <Button
                  onClick={handleSearch}
                  className="bg-blue-600 hover:bg-blue-700 text-white border-0 transition-all shadow-lg shadow-blue-600/20"
                >
                  Search
                </Button>
                {activeSearchTerm && (
                  <Button
                    onClick={handleClearSearch}
                    variant="outline"
                    className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white transition-all"
                  >
                    Clear
                  </Button>
                )}
              </div>
            </div>

            {/* Review Filter */}
            <div className="mb-6">
              <Button
                onClick={() => {
                  setShowReviewOnly(!showReviewOnly);
                  setCurrentPage(1);
                }}
                variant={showReviewOnly ? 'default' : 'outline'}
                className={
                  showReviewOnly
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-0 transition-all shadow-lg shadow-yellow-600/20'
                    : 'bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white transition-all'
                }
              >
                <Eye className="h-4 w-4 mr-2" />
                {showReviewOnly
                  ? 'Showing review items only'
                  : 'Show review items only'}
              </Button>
            </div>

            {/* Results Info */}
            <div className="mb-4 text-sm text-slate-400">
              {activeSearchTerm
                ? `Found ${totalIngredients} ingredients matching "${activeSearchTerm}" - showing ${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, totalIngredients)}`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, totalIngredients)} of ${totalIngredients} ingredients`}
            </div>

            {/* Top Pagination */}
            {totalPages > 1 && (
              <div className="mb-6 flex justify-between items-center">
                <div className="text-sm text-slate-400">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  >
                    Previous
                  </Button>

                  {/* Page numbers */}
                  <div className="flex space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum =
                        currentPage <= 3
                          ? i + 1
                          : currentPage >= totalPages - 2
                            ? totalPages - 4 + i
                            : currentPage - 2 + i;

                      if (pageNum < 1 || pageNum > totalPages) return null;

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            pageNum === currentPage ? 'default' : 'outline'
                          }
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className={
                            pageNum === currentPage
                              ? 'bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg shadow-blue-600/20'
                              : 'bg-slate-800/50 border-slate-700 text-slate-400 hover:bg-slate-700/50 hover:text-white transition-all'
                          }
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {/* Ingredients Table */}
            <div className="card-dark overflow-hidden">
              {ingredients.length === 0 ? (
                <div className="text-center py-12">
                  <Carrot className="mx-auto h-12 w-12 text-slate-400" />
                  <h3 className="mt-2 text-sm font-semibold text-white">
                    {activeSearchTerm
                      ? 'No ingredients match your search'
                      : 'No ingredients found'}
                  </h3>
                  <p className="mt-1 text-sm text-slate-400">
                    {activeSearchTerm
                      ? 'Try adjusting your search terms.'
                      : 'Ingredients will appear here once they are populated from meals.'}
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16 text-slate-400 font-medium">
                        Emoji
                      </TableHead>
                      <TableHead className="text-slate-400 font-medium">
                        Name
                      </TableHead>
                      <TableHead className="text-slate-400 font-medium">
                        Used in Meals
                      </TableHead>
                      <TableHead className="w-24 text-right text-slate-400 font-medium">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ingredients.map((ingredient, index) => (
                      <TableRow 
                        key={ingredient.id}
                        className={index % 2 === 1 ? 'bg-slate-800/50' : 'bg-slate-900/30'}
                      >
                        <TableCell className="text-2xl">
                          {ingredient.emoji || '🍽️'}
                        </TableCell>
                        <TableCell className="font-medium text-white">
                          <div className="flex items-center gap-2">
                            {ingredient.name}
                            {ingredient.needs_review && (
                              <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/30 rounded-full">
                                <Eye className="h-3 w-3 mr-1" />
                                Review
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-slate-300">
                          {(() => {
                            const isLoading = loadingMeals.has(ingredient.id);
                            const meals = ingredientMeals.get(ingredient.id);
                            const isExpanded = expandedIngredients.has(ingredient.id);
                            
                            // Debug logging
                            if (meals) {
                              console.log(`Displaying meals for ${ingredient.name} (${ingredient.id}):`, meals);
                            }
                            
                            if (!meals) {
                              // Show "View Meals" button
                              return (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => loadMealsForIngredient(ingredient)}
                                  disabled={isLoading}
                                  className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-blue-600/20 hover:text-blue-400 hover:border-blue-500/50 transition-all"
                                >
                                  {isLoading ? (
                                    <>
                                      <div className="mr-2 h-3 w-3 animate-spin rounded-full border border-slate-400 border-t-transparent" />
                                      Loading...
                                    </>
                                  ) : (
                                    <>
                                      <UtensilsCrossed className="h-3 w-3 mr-2" />
                                      View Meals
                                    </>
                                  )}
                                </Button>
                              );
                            }
                            
                            // Show loaded meals
                            if (meals.length === 0) {
                              return <span className="text-slate-400 text-sm">No meals found</span>;
                            }
                            
                            return (
                              <div>
                                <div className="mb-1 text-sm text-slate-400">
                                  Used in {meals.length} meal{meals.length !== 1 ? 's' : ''}
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {(() => {
                                    const mealsToShow = isExpanded ? meals : meals.slice(0, 5);
                                    
                                    return (
                                      <>
                                        {mealsToShow.map((meal) => (
                                          <Link
                                            key={meal.id}
                                            href={`/meals/${meal.id}/compare`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30 transition-colors"
                                          >
                                            {meal.name}
                                            <ExternalLink className="ml-1 h-3 w-3" />
                                          </Link>
                                        ))}
                                        {meals.length > 5 && (
                                          <button
                                            onClick={() => toggleExpandedIngredient(ingredient.id)}
                                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-slate-600/20 text-slate-400 border border-slate-600/30 hover:bg-slate-600/30 transition-colors cursor-pointer"
                                          >
                                            {isExpanded 
                                              ? 'Show less' 
                                              : `+${meals.length - 5} more`
                                            }
                                          </button>
                                        )}
                                      </>
                                    );
                                  })()}
                                </div>
                              </div>
                            );
                          })()}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingIngredient(ingredient)}
                              className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-blue-600/20 hover:text-blue-400 hover:border-blue-500/50 transition-all"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => setDeleteConfirmId(ingredient.id)}
                              className="bg-red-600/20 border-red-500/50 text-red-400 hover:bg-red-600/30 hover:border-red-500 transition-all"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>

            {/* Bottom Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-slate-400">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  >
                    Previous
                  </Button>

                  {/* Page numbers */}
                  <div className="flex space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum =
                        currentPage <= 3
                          ? i + 1
                          : currentPage >= totalPages - 2
                            ? totalPages - 4 + i
                            : currentPage - 2 + i;

                      if (pageNum < 1 || pageNum > totalPages) return null;

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            pageNum === currentPage ? 'default' : 'outline'
                          }
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className={
                            pageNum === currentPage
                              ? 'bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg shadow-blue-600/20'
                              : 'bg-slate-800/50 border-slate-700 text-slate-400 hover:bg-slate-700/50 hover:text-white transition-all'
                          }
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Edit Dialog */}
        <Dialog
          open={!!editingIngredient}
          onOpenChange={() => setEditingIngredient(null)}
        >
          <DialogContent className="bg-slate-800 border-slate-700">
            <DialogHeader>
              <DialogTitle className="text-white">
                {editingIngredient?.id
                  ? 'Edit Ingredient'
                  : 'Create New Ingredient'}
              </DialogTitle>
              <DialogDescription className="text-slate-300">
                {editingIngredient?.id ? 'Update' : 'Enter'} the ingredient
                details below.
              </DialogDescription>
            </DialogHeader>
            {editingIngredient && (
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <label
                    htmlFor="name"
                    className="text-sm font-medium text-slate-300"
                  >
                    Name
                  </label>
                  <Input
                    id="name"
                    value={editingIngredient.name}
                    onChange={(e) =>
                      setEditingIngredient({
                        ...editingIngredient,
                        name: e.target.value,
                      })
                    }
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
                <div className="grid gap-2">
                  <label
                    htmlFor="emoji"
                    className="text-sm font-medium text-slate-300"
                  >
                    Emoji
                  </label>
                  <EmojiPicker
                    value={editingIngredient.emoji || '🥗'}
                    onChange={(emoji) =>
                      setEditingIngredient({ ...editingIngredient, emoji })
                    }
                    placeholder="Select an emoji"
                  />
                </div>
              </div>
            )}
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setEditingIngredient(null)}
                className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:text-white transition-all"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={updateMutation.isPending || createMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg shadow-blue-600/20 disabled:opacity-50 disabled:shadow-none transition-all"
              >
                {updateMutation.isPending || createMutation.isPending
                  ? 'Saving...'
                  : editingIngredient?.id
                    ? 'Save Changes'
                    : 'Create Ingredient'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={!!deleteConfirmId}
          onOpenChange={() => setDeleteConfirmId(null)}
        >
          <DialogContent className="sm:max-w-md bg-slate-800 border-slate-700">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-400">
                <AlertTriangle className="h-5 w-5" />
                Confirm Deletion
              </DialogTitle>
              <DialogDescription className="text-slate-300">
                Are you sure you want to delete this ingredient? This action
                cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2 sm:gap-0">
              <Button
                variant="outline"
                onClick={() => setDeleteConfirmId(null)}
                className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:text-white transition-all"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => deleteConfirmId && handleDelete(deleteConfirmId)}
                disabled={deleteMutation.isPending}
                className="bg-red-600 hover:bg-red-700 text-white border-0 shadow-lg shadow-red-600/20 disabled:opacity-50 disabled:shadow-none transition-all"
              >
                {deleteMutation.isPending ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthGuard>
  );
}

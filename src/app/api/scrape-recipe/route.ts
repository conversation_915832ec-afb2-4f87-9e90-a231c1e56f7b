import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Use Claude's WebFetch tool to scrape the recipe
    const prompt = `
      Please extract the following recipe information from this webpage:
      
      1. Recipe name/title
      2. Description/summary
      3. Ingredients list with amounts and units (parse carefully)
      4. Instructions/steps (numbered)
      5. Prep time, cook time, total time
      6. Number of servings
      7. Difficulty level
      8. Cuisine type
      9. Course type (appetizer, main course, etc.)
      10. Main recipe image URL if available
      
      Please format the response as a JSON object with this structure:
      {
        "name": "Recipe Name",
        "description": "Recipe description",
        "ingredients": [
          {
            "name": "ingredient name",
            "amount": "1",
            "unit": "cup"
          }
        ],
        "instructions": [
          {
            "step": 1,
            "text": "Step instruction"
          }
        ],
        "prepTime": "15 minutes",
        "cookTime": "30 minutes", 
        "totalTime": "45 minutes",
        "servings": "4",
        "difficulty": "Easy",
        "cuisine": "Italian",
        "course": "Main Course",
        "image": "image_url_if_available"
      }
      
      Focus on accuracy, especially for ingredient amounts and units. If information is not available, omit that field rather than guessing.
      Look for structured data (JSON-LD, microdata) first, then fall back to scraping HTML content.
    `;

    // Since we're in a Next.js API route, we'll need to implement WebFetch functionality
    // For now, let's create a mock response and show how to integrate WebFetch
    
    // This is where you would use the WebFetch tool in Claude's environment
    // For demonstration, here's a mock response:
    const mockScrapedData = {
      name: "Sample Recipe from " + new URL(url).hostname,
      description: "This is a mock response. WebFetch integration needed.",
      ingredients: [
        {
          name: "Sample ingredient 1",
          amount: "1",
          unit: "cup"
        },
        {
          name: "Sample ingredient 2", 
          amount: "2",
          unit: "tablespoons"
        }
      ],
      instructions: [
        {
          step: 1,
          text: "This is a mock instruction. Implement WebFetch to get real data."
        }
      ],
      prepTime: "15 minutes",
      cookTime: "30 minutes",
      servings: "4",
      difficulty: "Medium",
      cuisine: "Unknown",
      course: "Main Course"
    };

    // Since WebFetch is only available in Claude's environment, 
    // we'll use a different approach for the admin interface
    // You could integrate with a recipe scraping service like:
    // - Recipe Parser API
    // - Edamam Recipe API  
    // - Custom Puppeteer/Playwright scraper
    // - Or manually use Claude with WebFetch for one-time scraping

    // For now, let's try a basic HTML scraping approach
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (!response.ok) {
      // Handle common scraping errors more gracefully
      let errorMessage = `HTTP ${response.status}`;
      if (response.statusText && response.statusText !== '<none>') {
        errorMessage += `: ${response.statusText}`;
      }
      
      if (response.status === 523) {
        errorMessage += ' - Origin server unreachable. The website may be down or blocking requests.';
      } else if (response.status === 403) {
        errorMessage += ' - Access forbidden. The website may be blocking scraping requests.';
      } else if (response.status === 429) {
        errorMessage += ' - Rate limited. Too many requests to the website.';
      }
      
      throw new Error(errorMessage);
    }
    
    const html = await response.text();
    
    // Enhanced recipe extraction
    const scrapedData = await extractRecipeData(html, url);

    return NextResponse.json(scrapedData);
  } catch (error) {
    console.error('Error scraping recipe:', error);
    return NextResponse.json(
      { 
        error: 'Failed to scrape recipe',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function extractRecipeData(html: string, url: string) {
  const domain = new URL(url).hostname.replace('www.', '');
  
  // Try to extract JSON-LD structured data first (works for most sites)
  const jsonLdMatches = html.match(/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>([\s\S]*?)<\/script>/gi);
  let recipeData = null;
  
  if (jsonLdMatches) {
    for (const match of jsonLdMatches) {
      try {
        const jsonContent = match.replace(/<script[^>]*>/i, '').replace(/<\/script>/i, '');
        const data = JSON.parse(jsonContent);
        
        // Handle arrays of structured data
        const items = Array.isArray(data) ? data : [data];
        
        for (const item of items) {
          if (item['@type'] === 'Recipe' || 
              (Array.isArray(item['@type']) && item['@type'].includes('Recipe'))) {
            recipeData = item;
            break;
          }
        }
        
        if (recipeData) break;
      } catch (e) {
        console.log('Failed to parse JSON-LD data:', e);
      }
    }
  }
  
  if (recipeData) {
    // Helper function to clean text of HTML entities and special characters
    const cleanText = (text: string) => {
      return text
        .replace(/(<([^>]+)>)/gi, '') // Remove HTML tags
        .replace(/&#x[\da-fA-F]+;/g, '') // Remove hex HTML entities like &#x2764;
        .replace(/&#\d+;/g, '') // Remove decimal HTML entities
        .replace(/&[a-zA-Z]+;/g, '') // Remove named HTML entities like &amp;
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    // Parse structured recipe data
    const parseIngredients = (ingredients: any[]) => {
      return ingredients.map((ing) => {
        if (typeof ing === 'string') {
          // Clean the ingredient string first
          const cleanIng = cleanText(ing);
          
          if (!cleanIng) return { name: '', amount: '', unit: '' };
          
          // Enhanced parsing for different ingredient formats
          // Try to match: amount + unit + ingredient name
          const patterns = [
            // "¼cupsflour" (fraction+unit+ingredient with no spaces) - Six Sisters specific
            /^([¼½¾⅓⅔⅛⅜⅝⅞])(cups?|cup|tablespoons?|tablespoon|tbsp|teaspoons?|teaspoon|tsp|pounds?|pound|lbs?|lb|ounces?|ounce|ozs?|oz|gallons?|gallon|gal|quarts?|quart|qt|pints?|pint|pt)(.+)$/i,
            // "2cups flour" (number+unit+ingredient with no spaces)
            /^([\d\/\.\s]+)(cups?|cup|tablespoons?|tablespoon|tbsp|teaspoons?|teaspoon|tsp|pounds?|pound|lbs?|lb|ounces?|ounce|ozs?|oz|gallons?|gallon|gal|quarts?|quart|qt|pints?|pint|pt)(.+)$/i,
            // "2 cups flour" or "2 1/2 cups flour" or "2½ cups flour" (normal spacing)
            /^([\d\/\.\s¼½¾⅓⅔⅛⅜⅝⅞]+)\s+([a-zA-Z]+(?:\s+[a-zA-Z]+)?)\s+(.+)$/,
            // "2 flour" (amount + ingredient, no unit)
            /^([\d\/\.\s¼½¾⅓⅔⅛⅜⅝⅞]+)\s+([^0-9].+)$/,
            // Just ingredient name
            /^(.+)$/
          ];
          
          for (let i = 0; i < patterns.length; i++) {
            const match = cleanIng.match(patterns[i]);
            if (match) {
              if (i === 0 || i === 1) {
                // Patterns 0 & 1: fraction/number+unit+ingredient (no spaces)
                return {
                  amount: match[1]?.trim() || '',
                  unit: match[2]?.trim() || '',
                  name: match[3]?.trim() || cleanIng
                };
              } else if (i === 2) {
                // Pattern 2: normal spacing with unit
                return {
                  amount: match[1]?.trim() || '',
                  unit: match[2]?.trim() || '',
                  name: match[3]?.trim() || cleanIng
                };
              } else if (i === 3) {
                // Pattern 3: amount + ingredient, no unit
                return {
                  amount: match[1]?.trim() || '',
                  unit: '',
                  name: match[2]?.trim() || cleanIng
                };
              } else {
                // Pattern 4: Just ingredient name
                return {
                  amount: '',
                  unit: '',
                  name: cleanIng
                };
              }
            }
          }
          
          return { name: cleanIng, amount: '', unit: '' };
        }
        return {
          name: cleanText(ing.name || ing.text || ''),
          amount: cleanText(ing.amount || ''),
          unit: cleanText(ing.unit || '')
        };
      }).filter(ing => ing.name); // Remove empty ingredients
    };
    
    const parseInstructions = (instructions: any[]) => {
      return instructions.map((inst, idx) => {
        const text = typeof inst === 'string' ? inst : (inst.text || inst.name || inst.description || '');
        const cleanedText = cleanText(text);
        return {
          step: idx + 1,
          text: cleanedText
        };
      }).filter(inst => inst.text); // Remove empty instructions
    };
    
    const parseTime = (time: string) => {
      if (!time) return '';
      // Convert ISO 8601 duration (PT15M) to human readable
      if (time.startsWith('PT')) {
        const hours = time.match(/(\d+)H/);
        const minutes = time.match(/(\d+)M/);
        let result = '';
        if (hours) result += hours[1] + ' hours ';
        if (minutes) result += minutes[1] + ' minutes';
        return result.trim();
      }
      return time;
    };
    
    // Extract all possible time fields
    const timeFields = {
      prepTime: cleanText(parseTime(recipeData.prepTime)),
      cookTime: cleanText(parseTime(recipeData.cookTime)),
      totalTime: cleanText(parseTime(recipeData.totalTime)),
      restTime: cleanText(parseTime(recipeData.restTime)),
      chillTime: cleanText(parseTime(recipeData.chillTime)),
      marinnateTime: cleanText(parseTime(recipeData.marinateTime)),
      bakingTime: cleanText(parseTime(recipeData.bakingTime)),
      boilTime: cleanText(parseTime(recipeData.boilTime)),
      freezeTime: cleanText(parseTime(recipeData.freezeTime)),
      activeTime: cleanText(parseTime(recipeData.activeTime)),
      inactiveTime: cleanText(parseTime(recipeData.inactiveTime)),
      // Pressure cooker / Instant Pot specific timing
      pressureTime: cleanText(parseTime(recipeData.pressureTime)),
      naturalRelease: cleanText(parseTime(recipeData.naturalRelease || recipeData.naturalReleaseTime)),
      quickRelease: cleanText(parseTime(recipeData.quickRelease || recipeData.quickReleaseTime)),
      pressureBuildTime: cleanText(parseTime(recipeData.pressureBuildTime)),
      releaseTime: cleanText(parseTime(recipeData.releaseTime))
    };

    return {
      name: cleanText(recipeData.name || 'Unknown Recipe'),
      description: cleanText(recipeData.description || ''),
      ingredients: recipeData.recipeIngredient ? parseIngredients(recipeData.recipeIngredient) : [],
      instructions: recipeData.recipeInstructions ? parseInstructions(recipeData.recipeInstructions) : [],
      // Legacy fields for backward compatibility
      prepTime: timeFields.prepTime,
      cookTime: timeFields.cookTime,
      totalTime: timeFields.totalTime,
      // New comprehensive time fields
      times: timeFields,
      servings: cleanText(recipeData.recipeYield || recipeData.yield || ''),
      difficulty: cleanText(recipeData.difficulty || ''),
      cuisine: cleanText(recipeData.recipeCuisine || ''),
      course: cleanText(recipeData.recipeCategory || ''),
      image: recipeData.image?.url || recipeData.image || '',
      note: `Parsed from ${domain} using JSON-LD structured data`
    };
  }
  
  // Domain-specific HTML parsing fallbacks for WPRM-powered sites
  const domainParsers = {
    'sixsistersstuff.com': parseWPRMRecipe,
    'twopeasandtheirpod.com': parseWPRMRecipe,
    'favfamilyrecipes.com': parseWPRMRecipe,
    'easyfamilyrecipes.com': parseWPRMRecipe,
    'melskitchencafe.com': parseWPRMRecipe,
    'abountifulkitchen.com': parseWPRMRecipe,
    'ourbestbites.com': parseWPRMRecipe
  };
  
  const parser = domainParsers[domain as keyof typeof domainParsers];
  if (parser) {
    const result = parser(html, url);
    if (result) {
      return {
        ...result,
        note: `Parsed from ${domain} using domain-specific WPRM parser`
      };
    }
  }
  
  // Generic HTML parsing fallback
  return parseGenericHTML(html, url);
}

// Parse WPRM (WordPress Recipe Maker) formatted recipes
function parseWPRMRecipe(html: string, url: string) {
  const domain = new URL(url).hostname;
  
  // Helper function to clean text (same as in main parser)
  const cleanText = (text: string) => {
    return text
      .replace(/(<([^>]+)>)/gi, '') // Remove HTML tags
      .replace(/&#x[\da-fA-F]+;/g, '') // Remove hex HTML entities like &#x2764;
      .replace(/&#\d+;/g, '') // Remove decimal HTML entities
      .replace(/&[a-zA-Z]+;/g, '') // Remove named HTML entities like &amp;
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  };
  
  // Extract recipe name
  const nameMatch = html.match(/<[^>]*class="[^"]*wprm-recipe-name[^"]*"[^>]*>(.*?)<\/[^>]*>/i);
  const name = nameMatch ? cleanText(nameMatch[1]) : `Recipe from ${domain}`;
  
  // Extract ingredients
  const ingredients: Array<{name: string, amount: string, unit: string}> = [];
  const ingredientRegex = /<li[^>]*class="[^"]*wprm-recipe-ingredient[^"]*"[^>]*>([\s\S]*?)<\/li>/gi;
  let match;
  
  while ((match = ingredientRegex.exec(html)) !== null) {
    const cleanedText = cleanText(match[1]);
    if (cleanedText) {
      // Enhanced parsing with multiple patterns (same as main parser)
      const patterns = [
        // "¼cupsflour" (fraction+unit+ingredient with no spaces) - Six Sisters specific
        /^([¼½¾⅓⅔⅛⅜⅝⅞])(cups?|cup|tablespoons?|tablespoon|tbsp|teaspoons?|teaspoon|tsp|pounds?|pound|lbs?|lb|ounces?|ounce|ozs?|oz|gallons?|gallon|gal|quarts?|quart|qt|pints?|pint|pt)(.+)$/i,
        // "2cups flour" (number+unit+ingredient with no spaces)
        /^([\d\/\.\s]+)(cups?|cup|tablespoons?|tablespoon|tbsp|teaspoons?|teaspoon|tsp|pounds?|pound|lbs?|lb|ounces?|ounce|ozs?|oz|gallons?|gallon|gal|quarts?|quart|qt|pints?|pint|pt)(.+)$/i,
        // "2 cups flour" or "2 1/2 cups flour" or "2½ cups flour" (normal spacing)
        /^([\d\/\.\s¼½¾⅓⅔⅛⅜⅝⅞]+)\s+([a-zA-Z]+(?:\s+[a-zA-Z]+)?)\s+(.+)$/,
        // "2 flour" (amount + ingredient, no unit)
        /^([\d\/\.\s¼½¾⅓⅔⅛⅜⅝⅞]+)\s+([^0-9].+)$/,
        // Just ingredient name
        /^(.+)$/
      ];
      
      for (let i = 0; i < patterns.length; i++) {
        const parts = cleanedText.match(patterns[i]);
        if (parts) {
          if (i === 0 || i === 1) {
            // Patterns 0 & 1: fraction/number+unit+ingredient (no spaces)
            ingredients.push({
              amount: parts[1]?.trim() || '',
              unit: parts[2]?.trim() || '',
              name: parts[3]?.trim() || cleanedText
            });
            break;
          } else if (i === 2) {
            // Pattern 2: normal spacing with unit
            ingredients.push({
              amount: parts[1]?.trim() || '',
              unit: parts[2]?.trim() || '',
              name: parts[3]?.trim() || cleanedText
            });
            break;
          } else if (i === 3) {
            // Pattern 3: amount + ingredient, no unit
            ingredients.push({
              amount: parts[1]?.trim() || '',
              unit: '',
              name: parts[2]?.trim() || cleanedText
            });
            break;
          } else {
            // Pattern 4: Just ingredient name
            ingredients.push({
              amount: '',
              unit: '',
              name: cleanedText
            });
            break;
          }
        }
      }
    }
  }
  
  // Extract instructions
  const instructions: Array<{step: number, text: string}> = [];
  const instructionRegex = /<li[^>]*class="[^"]*wprm-recipe-instruction[^"]*"[^>]*>([\s\S]*?)<\/li>/gi;
  let instructionMatch;
  
  let stepNum = 1;
  while ((instructionMatch = instructionRegex.exec(html)) !== null) {
    const cleanedText = cleanText(instructionMatch[1]);
    if (cleanedText) {
      instructions.push({
        step: stepNum++,
        text: cleanedText
      });
    }
  }
  
  // Extract servings
  const servingsMatch = html.match(/<[^>]*class="[^"]*wprm-recipe-servings[^"]*"[^>]*>(.*?)<\/[^>]*>/i);
  const servings = servingsMatch ? cleanText(servingsMatch[1]) : '';
  
  // Extract all possible time fields from WPRM
  const timePatterns = {
    prepTime: /<[^>]*class="[^"]*wprm-recipe-prep_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    cookTime: /<[^>]*class="[^"]*wprm-recipe-cook_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    totalTime: /<[^>]*class="[^"]*wprm-recipe-total_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    restTime: /<[^>]*class="[^"]*wprm-recipe-rest_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    chillTime: /<[^>]*class="[^"]*wprm-recipe-chill_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    marinateTime: /<[^>]*class="[^"]*wprm-recipe-marinate_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    bakingTime: /<[^>]*class="[^"]*wprm-recipe-baking_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    activeTime: /<[^>]*class="[^"]*wprm-recipe-active_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    inactiveTime: /<[^>]*class="[^"]*wprm-recipe-inactive_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    // Pressure cooker / Instant Pot specific timing
    pressureTime: /<[^>]*class="[^"]*wprm-recipe-pressure_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    naturalRelease: /<[^>]*class="[^"]*wprm-recipe-natural_release[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    quickRelease: /<[^>]*class="[^"]*wprm-recipe-quick_release[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    pressureBuildTime: /<[^>]*class="[^"]*wprm-recipe-pressure_build[^"]*"[^>]*>(.*?)<\/[^>]*>/i,
    releaseTime: /<[^>]*class="[^"]*wprm-recipe-release_time[^"]*"[^>]*>(.*?)<\/[^>]*>/i
  };

  const extractedTimes: any = {};
  for (const [timeType, pattern] of Object.entries(timePatterns)) {
    const match = html.match(pattern);
    extractedTimes[timeType] = match ? cleanText(match[1]) : '';
  }

  // Try to extract pressure cooker timing from instruction text if not found in structured fields
  if (!extractedTimes.naturalRelease) {
    const naturalReleaseMatch = html.match(/natural\s+release\s*:?\s*(\d+\s*(?:minutes?|mins?|hours?|hrs?))/i);
    if (naturalReleaseMatch) {
      extractedTimes.naturalRelease = naturalReleaseMatch[1].trim();
    } else if (html.match(/natural\s+release/i)) {
      // If we find "natural release" mentioned but no time, indicate it's required
      extractedTimes.naturalRelease = 'Required';
    }
  }

  if (!extractedTimes.quickRelease && html.match(/quick\s+release/i)) {
    extractedTimes.quickRelease = 'Required';
  }

  if (!extractedTimes.pressureTime) {
    const pressureMatch = html.match(/(?:pressure\s+cook|high\s+pressure)\s*:?\s*(\d+\s*(?:minutes?|mins?|hours?|hrs?))/i);
    if (pressureMatch) {
      extractedTimes.pressureTime = pressureMatch[1].trim();
    }
  }
  
  if (ingredients.length > 0 || instructions.length > 0) {
    return {
      name,
      description: `Recipe from ${domain}`,
      ingredients: ingredients.filter(ing => ing.name), // Remove empty ingredients
      instructions: instructions.filter(inst => inst.text), // Remove empty instructions
      // Legacy fields for backward compatibility
      prepTime: extractedTimes.prepTime,
      cookTime: extractedTimes.cookTime,
      totalTime: extractedTimes.totalTime,
      // New comprehensive time fields
      times: extractedTimes,
      servings,
      difficulty: '',
      cuisine: '',
      course: '',
      image: ''
    };
  }
  
  return null;
}

// Generic HTML parsing fallback
function parseGenericHTML(html: string, url: string) {
  // Helper function to clean text (same as in other parsers)
  const cleanText = (text: string) => {
    return text
      .replace(/(<([^>]+)>)/gi, '') // Remove HTML tags
      .replace(/&#x[\da-fA-F]+;/g, '') // Remove hex HTML entities like &#x2764;
      .replace(/&#\d+;/g, '') // Remove decimal HTML entities
      .replace(/&[a-zA-Z]+;/g, '') // Remove named HTML entities like &amp;
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  };

  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
  const name = titleMatch ? cleanText(titleMatch[1]).replace(/\s*-.*$/, '').replace(/\s*\|.*$/, '').trim() : 'Recipe from ' + new URL(url).hostname;
  
  // Try to find ingredients in common HTML patterns
  const findIngredients = () => {
    const patterns = [
      /<li[^>]*class="[^"]*ingredient[^"]*"[^>]*>(.*?)<\/li>/gi,
      /<div[^>]*class="[^"]*ingredient[^"]*"[^>]*>(.*?)<\/div>/gi,
      /<p[^>]*class="[^"]*ingredient[^"]*"[^>]*>(.*?)<\/p>/gi
    ];
    
    for (const pattern of patterns) {
      const matches = Array.from(html.matchAll(pattern));
      if (matches.length > 0) {
        return matches.map(match => {
          const cleanedText = cleanText(match[1]);
          return {
            name: cleanedText,
            amount: '',
            unit: ''
          };
        }).filter(ing => ing.name);
      }
    }
    
    return [{
      name: 'Could not extract ingredients automatically',
      amount: '',
      unit: ''
    }];
  };
  
  return {
    name,
    description: `Recipe from ${new URL(url).hostname}`,
    ingredients: findIngredients(),
    instructions: [
      {
        step: 1,
        text: 'Instructions could not be extracted automatically. Please check the original source.'
      }
    ],
    note: 'Basic HTML scraping - limited data available'
  };
}
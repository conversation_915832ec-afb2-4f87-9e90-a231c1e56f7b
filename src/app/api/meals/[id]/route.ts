import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const updates = await request.json();

    console.log('=== PATCH REQUEST START ===');
    console.log('Raw ID from params:', id);
    console.log('ID type:', typeof id);
    console.log('Updates received:', JSON.stringify(updates, null, 2));

    if (!id) {
      console.log('ERROR: No ID provided');
      return NextResponse.json(
        { error: 'Meal ID is required' },
        { status: 400 }
      );
    }

    // First check if meal exists with detailed logging
    console.log('Checking if meal exists...');
    const { data: existingMeal, error: existError } = await supabase
      .from('meals')
      .select('*')
      .eq('id', id);

    console.log('Existence check result:', { existingMeal, existError });

    if (existError) {
      console.error('Error checking meal existence:', existError);
      return NextResponse.json({ error: existError.message }, { status: 500 });
    }

    if (!existingMeal || existingMeal.length === 0) {
      console.log('Meal not found with ID:', id);
      return NextResponse.json({ error: 'Meal not found' }, { status: 404 });
    }

    console.log('Found meal:', existingMeal[0]);

    // Attempt the update with detailed logging
    console.log('Attempting update...');
    const { data, error, count } = await supabase
      .from('meals')
      .update(updates)
      .eq('id', id)
      .select();

    console.log('Update query result:', { data, error, count });
    console.log('Number of rows returned:', data?.length || 0);

    if (error) {
      console.error('Update error details:', JSON.stringify(error, null, 2));
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!data || data.length === 0) {
      console.error('Update returned no rows - this should not happen');
      return NextResponse.json(
        { error: 'Update failed - no rows affected' },
        { status: 500 }
      );
    }

    if (data.length > 1) {
      console.warn('Update returned multiple rows - using first one');
    }

    const updatedMeal = data[0];
    console.log('Successfully updated meal:', updatedMeal);
    console.log('=== PATCH REQUEST END ===');

    return NextResponse.json({
      success: true,
      data: updatedMeal,
    });
  } catch (error) {
    console.error('API: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Meal ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Deleting meal ${id}`);

    const { error, data } = await supabase
      .from('meals')
      .delete()
      .eq('id', id)
      .select();

    if (error) {
      console.error('API: Delete error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`API: Delete result:`, data);

    return NextResponse.json({
      success: true,
      deleted: data?.length || 0,
      data,
    });
  } catch (error) {
    console.error('API: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

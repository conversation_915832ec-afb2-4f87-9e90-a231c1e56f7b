import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    const { data, error } = await supabase
      .from('ingredients_for_review')
      .select('*')
      .is('processed_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching ingredients for review:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in ingredients-for-review GET route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ingredientName } = body;

    const { data, error } = await supabase
      .from('ingredients_for_review')
      .insert([{ ingredient_name: ingredientName }])
      .select();

    if (error) {
      console.error('Error adding ingredient for review:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in ingredients-for-review POST route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ingredientName = searchParams.get('ingredient');

    if (!ingredientName) {
      return NextResponse.json(
        { error: 'Ingredient name is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('ingredients_for_review')
      .delete()
      .eq('ingredient_name', ingredientName)
      .select();

    if (error) {
      console.error('Error removing ingredient from review:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in ingredients-for-review DELETE route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

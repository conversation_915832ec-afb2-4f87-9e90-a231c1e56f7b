'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';

export default function HomePage() {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();
  const hasRedirected = useRef(false);

  useEffect(() => {
    console.log('Root page auth state:', {
      user: user?.email,
      loading,
      isAdmin,
      hasRedirected: hasRedirected.current,
    });

    // Only redirect once and when not loading
    if (!loading && !hasRedirected.current) {
      hasRedirected.current = true;

      if (!user) {
        console.log('No user, redirecting to auth');
        router.replace('/auth');
      } else if (isAdmin) {
        console.log('Admin user, redirecting to meals');
        router.replace('/meals');
      } else {
        console.log('Non-admin user, redirecting to auth with error');
        router.replace('/auth?error=access_denied');
      }
    }
  }, [user, loading, isAdmin, router]);

  // Show loading while determining auth state
  return (
    <div className="min-h-screen bg-gradient-admin flex items-center justify-center">
      <div className="card-dark p-8">
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 bg-blue-600 rounded-full animate-pulse"></div>
          <span className="text-white">Loading...</span>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';
import {
  Eye,
  ExternalLink,
  Loader2,
  RefreshCw,
  AlertCircle,
  Check,
} from 'lucide-react';

interface ReviewIngredient {
  id: string;
  ingredient_name: string;
  created_at: string;
  usedInMeals: Array<{
    id: string;
    name: string;
  }>;
}

export default function ReviewIngredientsPage() {
  const queryClient = useQueryClient();
  const [removingIds, setRemovingIds] = useState<Set<string>>(new Set());

  // Fetch review ingredients
  const {
    data: reviewIngredients = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['reviewIngredients'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/ingredients-for-review');
        if (!response.ok) {
          throw new Error('Failed to fetch review ingredients');
        }
        const result = await response.json();
        return result.data || [];
      } catch (error) {
        console.error('Error fetching review ingredients:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000,
  });

  // Fetch meals data to get ingredient usage
  const { data: mealsData } = useQuery({
    queryKey: ['mealsForReviewIngredients'],
    queryFn: async () => {
      if (!reviewIngredients.length) return [];

      const { data: meals, error } = await supabase
        .from('meals')
        .select('id, name, ingredients');

      if (error) throw error;
      return meals || [];
    },
    enabled: reviewIngredients.length > 0,
    staleTime: 10 * 60 * 1000,
  });

  // Handle marking item as complete (remove from review)
  const handleMarkComplete = async (id: string) => {
    setRemovingIds((prev) => new Set(prev).add(id));

    try {
      const { error } = await supabase
        .from('ingredients_for_review')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Optimistically update the UI
      queryClient.setQueryData(['reviewIngredients'], (old: any[]) =>
        old.filter((item) => item.id !== id)
      );

      // Refetch to ensure consistency
      refetch();
    } catch (error) {
      console.error('Error removing ingredient from review:', error);
    } finally {
      setRemovingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  // Process review ingredients with meal data
  const reviewIngredientsWithMeals = reviewIngredients.map(
    (reviewItem: any) => {
      const usedInMeals: Array<{ id: string; name: string }> = [];

      if (mealsData) {
        mealsData.forEach((meal: any) => {
          if (meal.ingredients && Array.isArray(meal.ingredients)) {
            const hasIngredient = meal.ingredients.some((ingredient: any) => {
              if (typeof ingredient === 'string') {
                return (
                  ingredient.toLowerCase().trim() ===
                  reviewItem.ingredient_name.toLowerCase().trim()
                );
              }
              if (typeof ingredient === 'object' && ingredient.name) {
                return (
                  ingredient.name.toLowerCase().trim() ===
                  reviewItem.ingredient_name.toLowerCase().trim()
                );
              }
              return false;
            });

            if (hasIngredient) {
              usedInMeals.push({
                id: meal.id,
                name: meal.name,
              });
            }
          }
        });
      }

      return {
        ...reviewItem,
        usedInMeals,
      };
    }
  );

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <main className="flex-1 p-8">
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
            </div>
          </main>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          <div className="p-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-4">
                <Eye className="h-8 w-8 text-yellow-400" />
                <div>
                  <h1 className="text-3xl font-bold text-white">
                    Review Ingredients
                  </h1>
                  <p className="text-slate-400 mt-1">
                    Ingredients marked for manual review and processing
                  </p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  className="bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="card-dark p-6">
                <div className="flex items-center">
                  <Eye className="h-8 w-8 text-yellow-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-slate-400">
                      Total in Review
                    </p>
                    <p className="text-2xl font-bold text-white">
                      {reviewIngredientsWithMeals.length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Ingredients Table */}
            {reviewIngredientsWithMeals.length === 0 ? (
              <div className="card-dark p-12 text-center">
                <AlertCircle className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  No Ingredients in Review
                </h3>
                <p className="text-slate-400">
                  No ingredients are currently marked for review.
                </p>
              </div>
            ) : (
              <div className="card-dark overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-slate-800/50">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Ingredient Name
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Used in Meals
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Date Added
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-700">
                      {reviewIngredientsWithMeals.map(
                        (ingredient: ReviewIngredient) => (
                          <tr
                            key={ingredient.id}
                            className="hover:bg-slate-800/30"
                          >
                            <td className="px-6 py-4">
                              <div className="font-mono text-yellow-400 bg-yellow-900/20 border border-yellow-500/30 rounded px-2 py-1 inline-block">
                                &quot;{ingredient.ingredient_name}&quot;
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              {ingredient.usedInMeals.length === 0 ? (
                                <span className="text-slate-500 italic">
                                  No meals found
                                </span>
                              ) : (
                                <div className="space-y-1">
                                  {ingredient.usedInMeals
                                    .slice(0, 3)
                                    .map((meal) => (
                                      <div key={meal.id}>
                                        <Link
                                          href={`/meals/${meal.id}`}
                                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30 transition-colors group"
                                        >
                                          <span className="truncate max-w-xs">
                                            {meal.name}
                                          </span>
                                          <ExternalLink className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                                        </Link>
                                      </div>
                                    ))}
                                  {ingredient.usedInMeals.length > 3 && (
                                    <div className="text-xs text-slate-400">
                                      +{ingredient.usedInMeals.length - 3} more
                                      meals
                                    </div>
                                  )}
                                </div>
                              )}
                            </td>
                            <td className="px-6 py-4 text-slate-400 text-sm">
                              {new Date(
                                ingredient.created_at
                              ).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4">
                              <Button
                                onClick={() =>
                                  handleMarkComplete(ingredient.id)
                                }
                                disabled={removingIds.has(ingredient.id)}
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white border-0"
                              >
                                {removingIds.has(ingredient.id) ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <>
                                    <Check className="mr-1 h-4 w-4" />
                                    Complete
                                  </>
                                )}
                              </Button>
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </AuthGuard>
  );
}

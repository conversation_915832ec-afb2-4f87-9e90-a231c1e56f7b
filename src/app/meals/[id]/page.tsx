'use client';

import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { MealForm } from '@/components/meal-form';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { type Meal } from '@/lib/supabase';
import { use } from 'react';

interface MealDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function MealDetailPage({ params }: MealDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);

  // Fetch meal data
  const {
    data: meal,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['meal', resolvedParams.id],
    queryFn: async (): Promise<Meal> => {
      const { data, error } = await supabase
        .from('meals')
        .select('*')
        .eq('id', resolvedParams.id)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Meal not found');

      return data;
    },
  });

  const handleEditSuccess = () => {
    router.push('/meals');
  };

  const handleEditCancel = () => {
    router.push('/meals');
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-slate-400">Loading meal...</p>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (error) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-400">
                Error loading meal: {error.message}
              </p>
              <Button
                onClick={() => router.back()}
                className="mt-4 bg-blue-600 hover:bg-blue-700"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (!meal) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <p className="text-slate-400">Meal not found</p>
              <Button
                onClick={() => router.back()}
                className="mt-4 bg-blue-600 hover:bg-blue-700"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="p-8 pb-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="border-slate-700 text-slate-300 hover:bg-slate-700"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Meals
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Edit Meal: {meal.name}
                </h1>
                <p className="text-slate-400 text-base">
                  Update meal details below
                </p>
              </div>
            </div>
          </div>

          {/* Content - Full height form */}
          <div className="flex-1 px-8 pb-8">
            <MealForm
              meal={meal}
              onSuccess={handleEditSuccess}
              onCancel={handleEditCancel}
            />
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}

'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { MealForm } from '@/components/meal-form';
import { ArrowLeft, RefreshCw, ExternalLink, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { type Meal } from '@/lib/supabase';

interface ScrapedRecipe {
  name?: string;
  description?: string;
  ingredients?: Array<{
    name: string;
    amount?: string;
    unit?: string;
  }>;
  instructions?: Array<{
    step: number;
    text: string;
  }>;
  prepTime?: string;
  cookTime?: string;
  totalTime?: string;
  times?: {
    prepTime?: string;
    cookTime?: string;
    totalTime?: string;
    restTime?: string;
    chillTime?: string;
    marinateTime?: string;
    bakingTime?: string;
    boilTime?: string;
    freezeTime?: string;
    activeTime?: string;
    inactiveTime?: string;
    pressureTime?: string;
    naturalRelease?: string;
    quickRelease?: string;
    pressureBuildTime?: string;
    releaseTime?: string;
  };
  servings?: string;
  difficulty?: string;
  cuisine?: string;
  course?: string;
  image?: string;
  note?: string;
  error?: string;
}

export default function MealComparePage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const mealId = Array.isArray(params.id) ? params.id[0] : params.id;
  const [scrapedData, setScrapedData] = useState<ScrapedRecipe | null>(null);
  const [isLoadingScraped, setIsLoadingScraped] = useState(false);
  const [scrapingError, setScrapingError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(false);

  // Function to handle back navigation with preserved pagination
  const handleBackNavigation = () => {
    // Get the current search params (which were passed from the meals page)
    const returnParams = searchParams.toString();
    const mealsUrl = returnParams ? `/meals?${returnParams}` : '/meals';
    router.push(mealsUrl);
  };

  // Fetch meal data
  const { data: meal, isLoading: isLoadingMeal, error: mealError } = useQuery({
    queryKey: ['meal', mealId],
    queryFn: async (): Promise<Meal> => {
      console.log('🔍 Fetching meal data for ID:', mealId);
      const { data, error } = await supabase
        .from('meals')
        .select('*')
        .eq('id', mealId)
        .single();

      if (error) throw error;
      
      // Debug log the ingredients
      if (data && data.ingredients) {
        console.log('🥘 Meal ingredients from database:', data.ingredients);
        const cannelliniIngredients = data.ingredients.filter((ing: any) => 
          ing.name && ing.name.toLowerCase().includes('cannellini')
        );
        if (cannelliniIngredients.length > 0) {
          console.log('🫘 Cannellini ingredients found:', cannelliniIngredients);
        }
      }
      
      return data;
    },
    enabled: !!mealId,
    staleTime: 0, // Force fresh data
    gcTime: 0, // Don't cache
  });

  // Function to scrape recipe data
  const scrapeRecipeData = async (url: string) => {
    setIsLoadingScraped(true);
    setScrapingError(null);
    setScrapedData(null); // Clear previous data

    try {
      console.log('Scraping recipe from:', url);
      
      const response = await fetch('/api/scrape-recipe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || `HTTP ${response.status}: ${response.statusText}`);
      }

      const scrapedRecipe = await response.json();
      console.log('Scraped recipe data:', scrapedRecipe);
      
      // Check if the response contains an error
      if (scrapedRecipe.error) {
        throw new Error(scrapedRecipe.error);
      }
      
      setScrapedData(scrapedRecipe);
    } catch (error) {
      console.error('Error scraping recipe:', error);
      let errorMessage = 'Failed to scrape recipe';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        // Provide more user-friendly error messages
        if (error.message.includes('HTTP 523')) {
          errorMessage = 'The website appears to be temporarily unavailable (server unreachable). Please try again later or check if the URL is still valid.';
        } else if (error.message.includes('HTTP 403')) {
          errorMessage = 'Access to this website is forbidden. The site may be blocking automated requests.';
        } else if (error.message.includes('HTTP 404')) {
          errorMessage = 'The recipe page was not found. The URL may have changed or been removed.';
        } else if (error.message.includes('HTTP 429')) {
          errorMessage = 'Too many requests to this website. Please wait a moment and try again.';
        } else if (error.message.includes('HTTP 500') || error.message.includes('HTTP 502') || error.message.includes('HTTP 503')) {
          errorMessage = 'The website is experiencing server issues. Please try again later.';
        }
      }
      
      setScrapingError(errorMessage);
    } finally {
      setIsLoadingScraped(false);
    }
  };


  // Auto-scrape when meal loads (with error handling)
  useEffect(() => {
    if (meal?.source_url && !scrapedData && !isLoadingScraped && !scrapingError) {
      // Skip auto-scraping for known problematic domains or if we've already had an error
      const url = new URL(meal.source_url);
      const knownProblematicDomains = [
        'abountifulkitchen.com', // Currently experiencing server issues
      ];
      
      if (knownProblematicDomains.includes(url.hostname)) {
        setScrapingError(`The website ${url.hostname} appears to be temporarily unavailable. You can try manually scraping or check the original URL.`);
        return;
      }
      
      scrapeRecipeData(meal.source_url);
    }
  }, [meal?.source_url, scrapedData, isLoadingScraped, scrapingError]);

  // Handle save success from MealForm
  const handleMealSaveSuccess = () => {
    console.log('Meal updated successfully');
    setIsSaving(false);
    setSaveSuccess(true);
    setSaveError(false);
    // Clear success state after 3 seconds
    setTimeout(() => setSaveSuccess(false), 3000);
  };

  // Handle save error
  const handleMealSaveError = () => {
    setIsSaving(false);
    setSaveError(true);
    setSaveSuccess(false);
    // Clear error state after 5 seconds
    setTimeout(() => setSaveError(false), 5000);
  };

  // Handle form submission from header button
  const handleHeaderSave = async () => {
    setIsSaving(true);
    setSaveSuccess(false);
    setSaveError(false);
    const form = document.querySelector('#meal-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };


  if (isLoadingMeal) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-slate-400">Loading meal...</p>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (mealError || !meal) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-32 w-32 text-red-500" />
              <p className="mt-4 text-red-400">Error loading meal</p>
              <Button
                onClick={handleBackNavigation}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (!meal.source_url) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-32 w-32 text-yellow-500" />
              <p className="mt-4 text-yellow-400">No source URL available for comparison</p>
              <Button
                onClick={handleBackNavigation}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 overflow-hidden">
          {/* Header */}
          <div className="border-b border-slate-700 bg-slate-900/50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBackNavigation}
                  className="bg-slate-800/50 border-slate-700 text-slate-300 hover:bg-slate-700/50 hover:text-white"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-xl font-bold text-white">Compare Recipe</h1>
                  <p className="text-sm text-slate-400">{meal.name}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Link
                  href={meal.source_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Original
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => scrapeRecipeData(meal.source_url!)}
                  disabled={isLoadingScraped}
                  className="bg-blue-600/20 border-blue-500/50 text-blue-400 hover:bg-blue-600/30 hover:border-blue-500"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingScraped ? 'animate-spin' : ''}`} />
                  Re-scrape
                </Button>
                <Button
                  type="button"
                  onClick={handleHeaderSave}
                  disabled={isSaving}
                  className={`border-0 shadow-lg transition-all text-white ${
                    saveSuccess 
                      ? 'bg-emerald-600 hover:bg-emerald-700 shadow-emerald-600/20' 
                      : saveError
                      ? 'bg-red-600 hover:bg-red-700 shadow-red-600/20'
                      : 'bg-green-600 hover:bg-green-700 shadow-green-600/20'
                  } ${isSaving ? 'opacity-50' : ''}`}
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 mr-2 border-b-2 border-white"></div>
                      Saving...
                    </>
                  ) : saveSuccess ? (
                    <>
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Saved!
                    </>
                  ) : saveError ? (
                    <>
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      Error!
                    </>
                  ) : (
                    <>
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Update Meal
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Main Content - Side by Side */}
          <div className="flex h-full">
            {/* Left Side - Editable Meal Form (60%) */}
            <div className="w-3/5 border-r border-slate-700 overflow-auto">
              <div className="p-6">
                <div className="mb-4">
                  <h2 className="text-lg font-semibold text-white mb-2">
                    Your Recipe (Editable)
                  </h2>
                  <p className="text-sm text-slate-400">
                    Make changes to match the original recipe
                  </p>
                </div>
                <MealForm
                  meal={meal}
                  onSuccess={handleMealSaveSuccess}
                  onError={handleMealSaveError}
                  onCancel={handleBackNavigation}
                />
              </div>
            </div>

            {/* Right Side - Scraped Recipe Data (40%) */}
            <div className="w-2/5 overflow-auto bg-slate-800/30">
              <div className="p-6">
                <div className="mb-4">
                  <h2 className="text-lg font-semibold text-white mb-2">
                    Original Recipe
                  </h2>
                  <p className="text-sm text-slate-400">
                    Data scraped from source website
                  </p>
                </div>

                {isLoadingScraped ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="mt-4 text-slate-400">Scraping recipe data...</p>
                  </div>
                ) : scrapingError ? (
                  <div className="text-center py-8">
                    <AlertTriangle className="mx-auto h-16 w-16 text-amber-500" />
                    <p className="mt-4 text-amber-400 font-medium">Unable to scrape recipe</p>
                    <p className="mt-2 text-sm text-slate-300 max-w-md mx-auto leading-relaxed">{scrapingError}</p>
                    <div className="mt-6 flex flex-col gap-2">
                      <Button
                        onClick={() => {
                          setScrapingError(null);
                          scrapeRecipeData(meal.source_url!);
                        }}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        size="sm"
                      >
                        Try Again
                      </Button>
                      <Link
                        href={meal.source_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors"
                      >
                        <Button variant="outline" size="sm" className="w-full">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Original Site
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : scrapedData ? (
                  <div className="space-y-6">
                    {/* Debug info */}
                    {scrapedData.note && (
                      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-md p-3">
                        <p className="text-yellow-400 text-xs">{scrapedData.note}</p>
                      </div>
                    )}
                    
                    {/* Recipe Name */}
                    {scrapedData.name && (
                      <div>
                        <h3 className="text-base font-medium text-slate-300 mb-2">Name</h3>
                        <p className="text-white text-xl font-medium">{scrapedData.name}</p>
                      </div>
                    )}

                    {/* Description */}
                    {scrapedData.description && (
                      <div>
                        <h3 className="text-base font-medium text-slate-300 mb-2">Description</h3>
                        <p className="text-slate-200 text-xl">{scrapedData.description}</p>
                      </div>
                    )}

                    {/* Times Section */}
                    {(() => {
                      // Get all available time fields
                      const timeFields = scrapedData.times || {
                        prepTime: scrapedData.prepTime,
                        cookTime: scrapedData.cookTime,
                        totalTime: scrapedData.totalTime
                      };
                      
                      // Filter out empty time fields and format labels
                      const availableTimes = Object.entries(timeFields)
                        .filter(([_, value]) => value && value.trim())
                        .map(([key, value]) => ({
                          key,
                          label: key
                            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                            .replace(/^./, str => str.toUpperCase()), // Capitalize first letter
                          value
                        }));

                      if (availableTimes.length === 0) return null;

                      return (
                        <div>
                          <h3 className="text-base font-medium text-slate-300 mb-3">Timing</h3>
                          <div className="grid grid-cols-2 gap-4">
                            {availableTimes.map(({ key, label, value }) => (
                              <div key={key}>
                                <h4 className="text-sm font-medium text-slate-400 mb-1">{label}</h4>
                                <p className="text-slate-200 text-xl">{value}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })()}

                    {/* Other Meta Info */}
                    <div className="grid grid-cols-2 gap-4">
                      {scrapedData.servings && (
                        <div>
                          <h4 className="text-sm font-medium text-slate-400 mb-1">Servings</h4>
                          <p className="text-slate-200 text-xl">{scrapedData.servings}</p>
                        </div>
                      )}
                      {scrapedData.difficulty && (
                        <div>
                          <h4 className="text-sm font-medium text-slate-400 mb-1">Difficulty</h4>
                          <p className="text-slate-200 text-xl">{scrapedData.difficulty}</p>
                        </div>
                      )}
                      {scrapedData.cuisine && (
                        <div>
                          <h4 className="text-sm font-medium text-slate-400 mb-1">Cuisine</h4>
                          <p className="text-slate-200 text-xl">{scrapedData.cuisine}</p>
                        </div>
                      )}
                      {scrapedData.course && (
                        <div>
                          <h4 className="text-sm font-medium text-slate-400 mb-1">Course</h4>
                          <p className="text-slate-200 text-xl">{scrapedData.course}</p>
                        </div>
                      )}
                    </div>

                    {/* Ingredients */}
                    {scrapedData.ingredients && scrapedData.ingredients.length > 0 && (
                      <div>
                        <h3 className="text-base font-medium text-slate-300 mb-3">Ingredients</h3>
                        <div className="space-y-2">
                          {scrapedData.ingredients.map((ingredient, index) => (
                            <div key={index} className="flex items-start gap-3 text-xl">
                              <span className="text-blue-400 font-mono min-w-0 flex-shrink-0 font-medium text-xl">
                                {ingredient.amount} {ingredient.unit}
                              </span>
                              <span className="text-slate-200 text-xl">{ingredient.name}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Instructions */}
                    {scrapedData.instructions && scrapedData.instructions.length > 0 && (
                      <div>
                        <h3 className="text-base font-medium text-slate-300 mb-3">Instructions</h3>
                        <div className="space-y-3">
                          {scrapedData.instructions.map((instruction, index) => (
                            <div key={index} className="flex gap-3">
                              <span className="text-blue-400 font-bold text-xl min-w-0 flex-shrink-0">
                                {instruction.step}.
                              </span>
                              <p className="text-slate-200 text-xl leading-relaxed">{instruction.text}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-slate-400">No recipe data available</p>
                    <Button
                      onClick={() => scrapeRecipeData(meal.source_url!)}
                      className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
                      size="sm"
                    >
                      Scrape Recipe
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

        </div>
      </div>
    </AuthGuard>
  );
}
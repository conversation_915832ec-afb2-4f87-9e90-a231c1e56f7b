'use client';

import { useState } from 'react';
import Image from 'next/image';
import { usePaginatedMeals, useDeleteMeal, useUpdateMeal } from '@/lib/queries';
import { AuthGuard } from '@/components/auth/auth-guard';
import { Sidebar } from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { MealForm } from '@/components/meal-form';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  UtensilsCrossed,
  ChevronUp,
  ChevronDown,
  AlertTriangle,
  ExternalLink,
} from 'lucide-react';
import Link from 'next/link';
import { Switch } from '@/components/ui/switch';
import { type Meal } from '@/lib/supabase';

export default function MealsPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchInput, setSearchInput] = useState(''); // What user types
  const [activeSearchTerm, setActiveSearchTerm] = useState(''); // What we actually search for
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [deletingMeal, setDeletingMeal] = useState<Meal | null>(null);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const {
    data: mealsData,
    isLoading,
    error,
    refetch,
  } = usePaginatedMeals(currentPage, 50, sortBy, sortOrder, activeSearchTerm);
  const deleteMeal = useDeleteMeal();
  const updateMeal = useUpdateMeal();

  const meals = mealsData?.data || [];

  const getCourseStyle = (course: string) => {
    switch (course?.toLowerCase()) {
      case 'appetizer':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'main course':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'side dish':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'dessert':
        return 'bg-pink-500/20 text-pink-400 border-pink-500/30';
      case 'beverage':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'snack':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'breakfast':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-gray-500/20 text-slate-400 border-gray-500/30';
    }
  };

  const handleDelete = async () => {
    if (!deletingMeal) return;

    try {
      await deleteMeal.mutateAsync(deletingMeal.id);
      setDeletingMeal(null);
    } catch (error) {
      console.error('Error deleting meal:', error);
    }
  };

  const handleFormSuccess = () => {
    setShowCreateDialog(false);
  };

  const handleStatusChange = async (mealId: string, newStatus: boolean) => {
    try {
      await updateMeal.mutateAsync({
        id: mealId,
        status: newStatus ? 'active' : 'inactive',
      });
    } catch (error) {
      console.error('Status update error:', error);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // Don't clear search when changing pages anymore
  };

  const handleSearch = () => {
    setActiveSearchTerm(searchInput);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setActiveSearchTerm('');
    setCurrentPage(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const SortableHeader = ({
    column,
    children,
  }: {
    column: string;
    children: React.ReactNode;
  }) => {
    const isActive = sortBy === column;
    return (
      <TableHead
        className="cursor-pointer hover:bg-slate-800/30 select-none text-slate-400 font-medium"
        onClick={() => handleSort(column)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          {isActive ? (
            sortOrder === 'asc' ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )
          ) : (
            <div className="h-4 w-4" /> // Placeholder for alignment
          )}
        </div>
      </TableHead>
    );
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-slate-400">Loading meals...</p>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (error) {
    return (
      <AuthGuard>
        <div className="flex h-screen bg-gradient-admin">
          <Sidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-400">
                Error loading meals: {error.message}
              </p>
              <p className="text-sm text-slate-400 mt-2">
                Make sure your Supabase connection is configured correctly.
              </p>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 overflow-auto">
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">Meals</h1>
                <p className="text-slate-400 text-base">
                  Manage your meal collection ({mealsData?.count || 0} total
                  meals)
                </p>
              </div>
              <Dialog
                open={showCreateDialog}
                onOpenChange={setShowCreateDialog}
              >
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white border-0">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Meal
                  </Button>
                </DialogTrigger>
                <DialogContent
                  className="max-h-[90vh] overflow-hidden"
                  style={{ width: '80vw', maxWidth: '80vw' }}
                >
                  <DialogHeader>
                    <DialogTitle>Create New Meal</DialogTitle>
                  </DialogHeader>
                  <MealForm
                    onSuccess={handleFormSuccess}
                    onCancel={() => setShowCreateDialog(false)}
                  />
                </DialogContent>
              </Dialog>
            </div>

            {/* Search */}
            <div className="mb-6">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Search by meal name..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-400"
                  />
                </div>
                <Button
                  onClick={handleSearch}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Search
                </Button>
                {activeSearchTerm && (
                  <Button
                    onClick={handleClearSearch}
                    variant="outline"
                    className="border-slate-700 text-slate-300 hover:bg-slate-700"
                  >
                    Clear
                  </Button>
                )}
              </div>
            </div>

            {/* Results Info */}
            {mealsData && (
              <div className="mb-4 text-sm text-slate-400">
                {activeSearchTerm
                  ? `Found ${mealsData.count} meals matching "${activeSearchTerm}" - showing ${(currentPage - 1) * 50 + 1}-${Math.min(currentPage * 50, mealsData.count)}`
                  : `Showing ${(currentPage - 1) * 50 + 1}-${Math.min(currentPage * 50, mealsData.count)} of ${mealsData.count} meals`}
              </div>
            )}

            {/* Meals Table */}
            <div className="card-dark overflow-hidden">
              {meals.length === 0 ? (
                <div className="text-center py-12">
                  <UtensilsCrossed className="mx-auto h-12 w-12 text-slate-400" />
                  <h3 className="mt-2 text-sm font-semibold text-white">
                    {activeSearchTerm
                      ? 'No meals match your search'
                      : 'No meals found'}
                  </h3>
                  <p className="mt-1 text-sm text-slate-400">
                    {activeSearchTerm
                      ? 'Try adjusting your search terms.'
                      : 'Get started by creating your first meal.'}
                  </p>
                  {!activeSearchTerm && (
                    <div className="mt-6">
                      <Dialog
                        open={showCreateDialog}
                        onOpenChange={setShowCreateDialog}
                      >
                        <DialogTrigger asChild>
                          <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Meal
                          </Button>
                        </DialogTrigger>
                        <DialogContent
                          className="max-h-[90vh] overflow-hidden"
                          style={{ width: '80vw', maxWidth: '80vw' }}
                        >
                          <DialogHeader>
                            <DialogTitle>Create New Meal</DialogTitle>
                          </DialogHeader>
                          <MealForm
                            onSuccess={handleFormSuccess}
                            onCancel={() => setShowCreateDialog(false)}
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  )}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <SortableHeader column="status">Status</SortableHeader>
                      <TableHead className="text-slate-400 font-medium">
                        Image
                      </TableHead>
                      <SortableHeader column="name">Name</SortableHeader>
                      <SortableHeader column="course">Course</SortableHeader>
                      <TableHead className="text-right text-slate-400 font-medium">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {meals.map((meal) => (
                      <TableRow key={meal.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={meal.status === 'active'}
                              onCheckedChange={(checked) =>
                                handleStatusChange(meal.id, checked)
                              }
                            />
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                meal.status === 'active'
                                  ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                  : meal.status === 'draft'
                                    ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                                    : 'bg-gray-500/20 text-slate-400 border border-gray-500/30'
                              }`}
                            >
                              {meal.status === 'active'
                                ? 'Complete'
                                : meal.status === 'draft'
                                  ? 'Draft'
                                  : 'Pending'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="w-16 h-16 bg-slate-700/50 rounded-lg overflow-hidden flex items-center justify-center">
                            {meal.image ? (
                              <Image
                                src={meal.image}
                                alt={meal.name}
                                width={64}
                                height={64}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                  const nextElement = e.currentTarget
                                    .nextElementSibling as HTMLElement;
                                  if (nextElement) {
                                    nextElement.style.display = 'flex';
                                  }
                                }}
                              />
                            ) : (
                              <UtensilsCrossed className="w-6 h-6 text-slate-400" />
                            )}
                            <div className="w-full h-full items-center justify-center hidden">
                              <UtensilsCrossed className="w-6 h-6 text-slate-400" />
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <Link href={`/meals/${meal.id}`} className="group">
                              <div className="font-medium text-white group-hover:text-blue-400 transition-colors flex items-center">
                                {meal.name}
                                <ExternalLink className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                              </div>
                              {meal.description && (
                                <div className="text-sm text-slate-400 truncate max-w-xs">
                                  {meal.description}
                                </div>
                              )}
                            </Link>
                          </div>
                        </TableCell>
                        <TableCell>
                          {meal.course ? (
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getCourseStyle(meal.course)}`}
                            >
                              {meal.course}
                            </span>
                          ) : (
                            <span className="text-slate-400 text-sm">-</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {meal.source_url ? (
                              <Link href={`/meals/${meal.id}/compare`}>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="bg-blue-600/20 border-blue-500/50 text-blue-400 hover:bg-blue-600/30 hover:border-blue-500"
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Button>
                              </Link>
                            ) : (
                              <Link href={`/meals/${meal.id}`}>
                                <Button variant="outline" size="sm">
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Button>
                              </Link>
                            )}
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => setDeletingMeal(meal)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>

            {/* Pagination */}
            {mealsData && mealsData.totalPages > 1 && (
              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-slate-400">
                  Page {currentPage} of {mealsData.totalPages}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    Previous
                  </Button>

                  {/* Page numbers */}
                  <div className="flex space-x-1">
                    {Array.from(
                      { length: Math.min(5, mealsData.totalPages) },
                      (_, i) => {
                        const pageNum =
                          currentPage <= 3
                            ? i + 1
                            : currentPage >= mealsData.totalPages - 2
                              ? mealsData.totalPages - 4 + i
                              : currentPage - 2 + i;

                        if (pageNum < 1 || pageNum > mealsData.totalPages)
                          return null;

                        return (
                          <Button
                            key={pageNum}
                            variant={
                              pageNum === currentPage ? 'default' : 'outline'
                            }
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      }
                    )}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= mealsData.totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={!!deletingMeal}
          onOpenChange={(open) => !open && setDeletingMeal(null)}
        >
          <DialogContent className="sm:max-w-md bg-slate-800 border-slate-700">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-red-400">
                <AlertTriangle className="h-5 w-5" />
                Confirm Deletion
              </DialogTitle>
              <DialogDescription className="text-slate-300">
                Are you sure you want to delete{' '}
                <span className="font-semibold text-white">
                  &quot;{deletingMeal?.name}&quot;
                </span>
                ? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2 sm:gap-0">
              <Button
                variant="outline"
                onClick={() => setDeletingMeal(null)}
                className="border-slate-600 text-white bg-transparent hover:bg-slate-700 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteMeal.isPending}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {deleteMeal.isPending ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Meal
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AuthGuard>
  );
}

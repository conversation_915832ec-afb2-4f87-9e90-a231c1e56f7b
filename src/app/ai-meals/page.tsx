'use client';

import { useState } from 'react';
import {
  useAIGeneratedMeals,
  useUpdateAIGeneratedMeal,
  useApproveAIGeneratedMeal,
} from '@/lib/queries';
import { Sidebar } from '@/components/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Search, Eye, CheckCircle, Clock, Sparkles } from 'lucide-react';
import { type AIGeneratedMeal } from '@/lib/supabase';

export default function AIGeneratedMealsPage() {
  const { data: aiMeals, isLoading, error } = useAIGeneratedMeals();
  const updateAIMeal = useUpdateAIGeneratedMeal();
  const approveAIMeal = useApproveAIGeneratedMeal();

  const [searchTerm, setSearchTerm] = useState('');
  const [viewingMeal, setViewingMeal] = useState<AIGeneratedMeal | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');

  const filteredMeals =
    aiMeals?.filter(
      (meal) =>
        meal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meal.course?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meal.cuisine_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        meal.user_query?.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  const handleApprove = async (meal: AIGeneratedMeal) => {
    try {
      await approveAIMeal.mutateAsync(meal.id);
      setViewingMeal(null);
    } catch (error) {
      console.error('Error approving meal:', error);
    }
  };

  const handleReject = async (meal: AIGeneratedMeal) => {
    try {
      await updateAIMeal.mutateAsync({
        id: meal.id,
        reviewed: true,
        approved_for_main_db: false,
        admin_notes: reviewNotes,
      });
      setViewingMeal(null);
      setReviewNotes('');
    } catch (error) {
      console.error('Error rejecting meal:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto"></div>
            <p className="mt-4 text-white/80">Loading AI-generated meals...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gradient-admin">
        <Sidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-300">
              Error loading AI meals: {error.message}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-admin">
      <Sidebar />
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2 flex items-center">
                <Sparkles className="mr-3 h-8 w-8 text-yellow-400" />
                AI-Generated Meals
              </h1>
              <p className="text-white/80 text-lg">
                Review and approve AI-generated meals for the main database
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="card-dark p-4 flex items-center space-x-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">
                    {filteredMeals.filter((m) => !m.reviewed).length}
                  </div>
                  <div className="text-xs text-orange-300">Pending Review</div>
                </div>
                <div className="w-px h-8 bg-slate-600"></div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {filteredMeals.filter((m) => m.approved_for_main_db).length}
                  </div>
                  <div className="text-xs text-green-300">Approved</div>
                </div>
              </div>
            </div>
          </div>

          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search AI meals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-400"
              />
            </div>
          </div>

          {/* AI Meals Table */}
          <div className="card-dark overflow-hidden">
            {filteredMeals.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-24 h-24 rounded-2xl flex items-center justify-center mx-auto mb-4 bg-slate-700/50">
                  <Sparkles className="h-12 w-12 text-slate-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {aiMeals?.length === 0
                    ? 'No AI meals found'
                    : 'No meals match your search'}
                </h3>
                <p className="text-slate-400 max-w-md mx-auto">
                  {aiMeals?.length === 0
                    ? 'AI-generated meals will appear here when users create them.'
                    : 'Try adjusting your search terms.'}
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-slate-400 font-medium">
                      Name
                    </TableHead>
                    <TableHead className="text-slate-400 font-medium">
                      Course
                    </TableHead>
                    <TableHead className="text-slate-400 font-medium">
                      Cuisine
                    </TableHead>
                    <TableHead className="text-slate-400 font-medium">
                      Times Generated
                    </TableHead>
                    <TableHead className="text-slate-400 font-medium">
                      Status
                    </TableHead>
                    <TableHead className="text-slate-400 font-medium">
                      Generated
                    </TableHead>
                    <TableHead className="text-right text-slate-400 font-medium">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMeals.map((meal) => (
                    <TableRow key={meal.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium text-white">
                            {meal.name}
                          </div>
                          {meal.user_query && (
                            <div className="text-sm text-slate-400 truncate max-w-xs">
                              Query: {meal.user_query}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-slate-400">
                        {meal.course || '-'}
                      </TableCell>
                      <TableCell className="text-slate-400">
                        {meal.cuisine_type || '-'}
                      </TableCell>
                      <TableCell className="text-slate-400">
                        {meal.times_generated || 1}
                      </TableCell>
                      <TableCell>
                        {meal.approved_for_main_db ? (
                          <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                            Approved
                          </Badge>
                        ) : meal.reviewed ? (
                          <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                            Rejected
                          </Badge>
                        ) : (
                          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                            Pending
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-slate-400">
                        {meal.generated_at
                          ? new Date(meal.generated_at).toLocaleDateString()
                          : '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          className="bg-blue-600 hover:bg-blue-700 text-white border-0"
                          size="sm"
                          onClick={() => setViewingMeal(meal)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </div>
      </div>

      {/* Review Dialog */}
      <Dialog
        open={!!viewingMeal}
        onOpenChange={(open) => !open && setViewingMeal(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Review AI-Generated Meal</DialogTitle>
          </DialogHeader>
          {viewingMeal && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-white">
                    {viewingMeal.name}
                  </h3>
                  <p className="text-sm text-slate-400">
                    {viewingMeal.course} • {viewingMeal.cuisine_type}
                  </p>
                  <p className="text-sm text-slate-400">
                    Spice level: {viewingMeal.spice_level}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-400">
                    Generated {viewingMeal.times_generated} times
                  </p>
                  <p className="text-sm text-slate-400">
                    Last:{' '}
                    {viewingMeal.last_generated_at
                      ? new Date(
                          viewingMeal.last_generated_at
                        ).toLocaleDateString()
                      : '-'}
                  </p>
                </div>
              </div>

              {viewingMeal.user_query && (
                <div>
                  <h4 className="font-medium text-white mb-2">
                    Original User Query
                  </h4>
                  <p className="text-sm text-slate-400 bg-slate-800/50 p-3 rounded">
                    {viewingMeal.user_query}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-2">Ingredients</h4>
                  <div className="text-sm text-slate-400 bg-slate-800/50 p-3 rounded max-h-40 overflow-y-auto">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(viewingMeal.ingredients, null, 2)}
                    </pre>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-white mb-2">Instructions</h4>
                  <div className="text-sm text-slate-400 bg-slate-800/50 p-3 rounded max-h-40 overflow-y-auto">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(viewingMeal.instructions, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-slate-400">
                    Prep Time
                  </label>
                  <p className="text-sm text-slate-400">
                    {viewingMeal.estimated_prep_time_min || 0} minutes
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-400">
                    Cook Time
                  </label>
                  <p className="text-sm text-slate-400">
                    {viewingMeal.estimated_cook_time_min || 0} minutes
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-400">
                    Difficulty
                  </label>
                  <p className="text-sm text-slate-400">
                    {viewingMeal.estimated_difficulty || '-'}
                  </p>
                </div>
              </div>

              {viewingMeal.dietary_tags &&
                viewingMeal.dietary_tags.length > 0 && (
                  <div>
                    <h4 className="font-medium text-white mb-2">
                      Dietary Tags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {viewingMeal.dietary_tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

              {!viewingMeal.reviewed && (
                <div>
                  <label
                    htmlFor="review-notes"
                    className="block text-sm font-medium text-slate-400 mb-2"
                  >
                    Review Notes (optional)
                  </label>
                  <Textarea
                    id="review-notes"
                    value={reviewNotes}
                    onChange={(e) => setReviewNotes(e.target.value)}
                    placeholder="Add any notes about this meal..."
                    rows={3}
                  />
                </div>
              )}

              {viewingMeal.admin_notes && (
                <div>
                  <h4 className="font-medium text-white mb-2">
                    Previous Review Notes
                  </h4>
                  <p className="text-sm text-slate-400 bg-slate-800/50 p-3 rounded">
                    {viewingMeal.admin_notes}
                  </p>
                </div>
              )}

              {!viewingMeal.reviewed && (
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => handleReject(viewingMeal)}
                    disabled={updateAIMeal.isPending}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    Reject
                  </Button>
                  <Button
                    onClick={() => handleApprove(viewingMeal)}
                    disabled={approveAIMeal.isPending}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Approve for Main DB
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

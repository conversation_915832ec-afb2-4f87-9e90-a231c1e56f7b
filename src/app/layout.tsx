import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { Toaster } from 'sonner';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'MenuMaker Admin',
  description: 'Admin interface for MenuMaker meal planner',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster
            theme="dark"
            position="top-right"
            toastOptions={{
              style: {
                background: 'rgb(51 65 85)',
                border: '1px solid rgb(71 85 105)',
                color: 'white',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}

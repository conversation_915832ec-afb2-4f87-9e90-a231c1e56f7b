'use client';

import { useState, forwardRef, useImperative<PERSON>andle } from 'react';
import { Button } from '@/components/ui/button';
import { GroupedIngredientsForm } from '@/components/ui/grouped-ingredients-form';
import { InstructionsForm } from '@/components/ui/instructions-form';
import { useUpdateMeal } from '@/lib/queries';
import { type Meal } from '@/lib/supabase';

interface Ingredient {
  name: string;
  amount: number | null;
  unit: string;
  emoji: string;
}

interface CompactMealFormProps {
  meal?: Meal;
  onSuccess?: () => void;
  onCancel?: () => void;
  onSaveStart?: () => void;
  onSaveSuccess?: () => void;
  onSaveError?: () => void;
}

export interface CompactMealFormRef {
  submit: () => void;
}

export const CompactMealForm = forwardRef<CompactMealFormRef, CompactMealFormProps>(({ meal, onSuccess, onCancel, onSaveStart, onSaveSuccess, onSaveError }, ref) => {
  // Normalize ingredient units from database
  const normalizeIngredients = (ingredients: any[]): Ingredient[] => {
    // Map database units to form units
    const unitMapping: { [key: string]: string } = {
      'ounce': 'oz',
      'ounces': 'oz',
      'tablespoon': 'tbs',
      'tablespoons': 'tbs',
      'teaspoon': 'tsp',
      'teaspoons': 'tsp',
      'pound': 'lb',
      'pounds': 'lbs',
      'fluid ounce': 'fl oz',
      'fluid ounces': 'fl oz',
    };

    return ingredients.map(ing => {
      const lowerUnit = ing.unit?.toLowerCase() || '';
      const mappedUnit = unitMapping[lowerUnit] || lowerUnit;
      
      return {
        ...ing,
        unit: mappedUnit,
        ingredient_id: ing.ingredient_id || ing.ingredientId, // Handle both formats
      };
    });
  };

  const [formData, setFormData] = useState({
    ingredients: normalizeIngredients(meal?.ingredients || []),
    ingredient_sections: (meal?.ingredient_sections || []) as any[],
    instructions: (meal?.instructions || []) as string[],
  });

  const updateMeal = useUpdateMeal();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!meal) return;

    // Call save start callback
    onSaveStart?.();

    // Capitalize units when saving back to database
    const capitalizeIngredients = (ingredients: Ingredient[]) => {
      // Reverse mapping from form units to database units
      const reverseUnitMapping: { [key: string]: string } = {
        'oz': 'Ounce',
        'tbs': 'Tablespoon',
        'tsp': 'Teaspoon',
        'lb': 'Pound',
        'lbs': 'Pounds',
        'fl oz': 'Fluid Ounce',
      };

      return ingredients.map(ing => {
        const dbUnit = reverseUnitMapping[ing.unit] || 
          (ing.unit ? ing.unit.charAt(0).toUpperCase() + ing.unit.slice(1) : '');
        
        return {
          ...ing,
          unit: dbUnit,
        };
      });
    };

    const mealData = {
      ingredients:
        formData.ingredients.length > 0 
          ? capitalizeIngredients(formData.ingredients) 
          : null,
      ingredient_sections:
        formData.ingredient_sections.length > 0
          ? formData.ingredient_sections
          : null,
      instructions:
        formData.instructions.length > 0 ? formData.instructions : null,
    };

    try {
      await updateMeal.mutateAsync({ id: meal.id, ...mealData });
      onSuccess?.();
      onSaveSuccess?.();
    } catch (error) {
      console.error('Error saving meal:', error);
      onSaveError?.();
    }
  };

  const isLoading = updateMeal.isPending;

  const updateFormData = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Expose submit method to parent component
  useImperativeHandle(ref, () => ({
    submit: () => handleSubmit(new Event('submit') as any)
  }));

  return (
    <div className="max-h-[85vh] overflow-y-auto bg-slate-900">
      <form onSubmit={handleSubmit} className="space-y-8 p-6">
        {/* Recipe Details */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
            👨‍🍳 Recipe Details
          </h3>

          <div className="space-y-8">
            <GroupedIngredientsForm
              ingredients={formData.ingredients}
              ingredientSections={formData.ingredient_sections}
              onChange={(ingredients) =>
                updateFormData('ingredients', ingredients)
              }
              onSectionsChange={(sections) =>
                updateFormData('ingredient_sections', sections)
              }
            />

            <InstructionsForm
              instructions={formData.instructions}
              onChange={(instructions) =>
                updateFormData('instructions', instructions)
              }
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="px-6 py-2 bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:text-white transition-all"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg shadow-blue-600/20 disabled:opacity-50 disabled:shadow-none transition-all"
            >
              {isLoading ? 'Saving...' : 'Update Recipe'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
});

CompactMealForm.displayName = 'CompactMealForm';
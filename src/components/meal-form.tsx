'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ImageUpload } from '@/components/ui/image-upload';
import { Select } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select';
import { GroupedIngredientsForm } from '@/components/ui/grouped-ingredients-form';
import { InstructionsForm } from '@/components/ui/instructions-form';
import { useCreateMeal, useUpdateMeal } from '@/lib/queries';
import { type Meal } from '@/lib/supabase';

interface Ingredient {
  name: string;
  amount: number | null;
  unit: string;
  emoji: string;
  ingredient_id?: string;
}

const COURSE_OPTIONS = [
  'Appetizer',
  'Beverage',
  'Breakfast',
  'Dessert',
  'Main Course',
  'Side Dish',
  'Snack',
];
const CUISINE_OPTIONS = [
  'American',
  'Argentinian',
  'Asian',
  'Brazilian',
  'British',
  'Cajun',
  'Caribbean',
  'Chinese',
  'Creole',
  'Cuban',
  'Ethiopian',
  'Filipino',
  'French',
  'Fusion',
  'German',
  'Greek',
  'Hungarian',
  'Indian',
  'Indonesian',
  'Italian',
  'Jamaican',
  'Japanese',
  'Korean',
  'Lebanese',
  'Mediterranean',
  'Mexican',
  'Middle Eastern',
  'Moroccan',
  'Peruvian',
  'Polish',
  'Portuguese',
  'Russian',
  'Scandinavian',
  'Southern',
  'Spanish',
  'Tex-Mex',
  'Thai',
  'Turkish',
  'Vietnamese',
];
const PREP_METHOD_OPTIONS = [
  'Air Fried',
  'Baked',
  'Boiled',
  'Braised',
  'Fried',
  'Grilled',
  'No Cook',
  'Pan-fried',
  'Roasted',
  'Sautéed',
  'Skillet',
  'Slow Cooked',
  'Steamed',
  'Stir-fried',
  'Stovetop',
  'Smoked',
];
const PROTEIN_OPTIONS = [
  'beans',
  'beef',
  'chicken',
  'duck',
  'eggs',
  'fish',
  'lamb',
  'mixed',
  'pork',
  'seafood',
  'tofu',
  'turkey',
  'vegan',
  'vegetarian',
];
const DIETARY_TAGS_OPTIONS = [
  'dairy_free',
  'egg_free',
  'gluten_free',
  'halal',
  'keto',
  'kosher',
  'low_carb',
  'low_fat',
  'low_sodium',
  'nut_free',
  'paleo',
  'pescatarian',
  'soy_free',
  'sugar_free',
  'vegan',
  'vegetarian',
  'whole30',
];
const ALLERGEN_OPTIONS = [
  'celery',
  'corn',
  'dairy',
  'eggs',
  'fish',
  'gluten',
  'lupin',
  'mollusks',
  'mustard',
  'nuts',
  'peanuts',
  'sesame',
  'shellfish',
  'soy',
  'sulfites',
  'tree_nuts',
  'wheat',
];
const EQUIPMENT_OPTIONS = [
  'air_fryer',
  'baking_dish',
  'baking_sheet',
  'blender',
  'cast_iron_skillet',
  'dutch_oven',
  'food_processor',
  'grill',
  'hand_mixer',
  'instant_pot',
  'loaf_pan',
  'mandoline',
  'microwave',
  'mixing_bowl',
  'muffin_tin',
  'none',
  'pizza_stone',
  'pressure_cooker',
  'rice_cooker',
  'skillet',
  'slow_cooker',
  'smoker',
  'sous_vide',
  'spiralizer',
  'springform_pan',
  'stand_mixer',
  'stock_pot',
  'toaster_oven',
  'wok',
];
const MEAL_TAGS_OPTIONS = [
  'budget_friendly',
  'comfort_food',
  'crowd_pleaser',
  'date_night',
  'easy',
  'family_friendly',
  'fancy',
  'freezer_friendly',
  'fusion',
  'game_day',
  'healthy',
  'holiday',
  'indulgent',
  'kid_friendly',
  'make_ahead',
  'no_cook',
  'one_pan',
  'one_pot',
  'picnic',
  'potluck',
  'quick',
  'seasonal',
  'sheet_pan',
  'traditional',
];

interface MealFormProps {
  meal?: Meal;
  onSuccess?: () => void;
  onError?: () => void;
  onCancel?: () => void;
}

export function MealForm({ meal, onSuccess, onError, onCancel }: MealFormProps) {
  // Normalize ingredient units from database
  const normalizeIngredients = (ingredients: any[]): Ingredient[] => {
    // Map database units to form units
    const unitMapping: { [key: string]: string } = {
      'ounce': 'oz',
      'ounces': 'oz',
      'tablespoon': 'tbs',
      'tablespoons': 'tbs',
      'teaspoon': 'tsp',
      'teaspoons': 'tsp',
      'pound': 'lb',
      'pounds': 'lbs',
      'fluid ounce': 'fl oz',
      'fluid ounces': 'fl oz',
    };

    return ingredients.map(ing => {
      const lowerUnit = ing.unit?.toLowerCase() || '';
      const mappedUnit = unitMapping[lowerUnit] || lowerUnit;
      
      return {
        ...ing,
        unit: mappedUnit,
        ingredient_id: ing.ingredient_id || ing.ingredientId, // Handle both formats
      };
    });
  };

  // Normalize prep method from database to match form options
  const normalizePrepMethod = (method: string | undefined): string => {
    if (!method) return '';
    
    // Handle compound methods - take the first/primary method
    const firstMethod = method.split(',')[0].trim();
    
    // Map database values to form values (comprehensive list based on actual data)
    const prepMethodMapping: { [key: string]: string } = {
      // Sautéing variations
      'sautéing': 'Sautéed',
      'sautéed': 'Sautéed',
      'sauteing': 'Sautéed',
      'saute': 'Sautéed',
      
      // Stir-frying variations
      'stir-frying': 'Stir-fried',
      'stir-fried': 'Stir-fried',
      'stir frying': 'Stir-fried',
      'stir-fry': 'Stir-fried',
      'stir fry': 'Stir-fried',
      
      // Pan-frying variations
      'pan-frying': 'Pan-fried',
      'pan-fried': 'Pan-fried',
      'pan fried': 'Pan-fried',
      'pan frying': 'Pan-fried',
      
      // Baking variations
      'baking': 'Baked',
      'baked': 'Baked',
      
      // Slow cooking variations
      'slow cooking': 'Slow Cooked',
      'slow cooker': 'Slow Cooked',
      'slow cooked': 'Slow Cooked',
      
      // Grilling variations
      'grilling': 'Grilled',
      'grilled': 'Grilled',
      'chargrilling': 'Grilled',
      'griddling': 'Grilled',
      
      // No cook variations
      'no-cook': 'No Cook',
      'no cook': 'No Cook',
      'chilled': 'No Cook',
      'refrigeration': 'No Cook',
      'refrigerate': 'No Cook',
      'chill': 'No Cook',
      'chilling': 'No Cook',
      'assembling': 'No Cook',
      'assembly': 'No Cook',
      'tossing': 'No Cook',
      'tossed': 'No Cook',
      'mixing': 'No Cook',
      'whisking': 'No Cook',
      'blending': 'No Cook',
      'mashing': 'No Cook',
      'layering': 'No Cook',
      
      // Pressure cooking variations
      'pressure cooker': 'Pressure Cooker',
      'instant pot': 'Pressure Cooker',
      'pressure cooking': 'Pressure Cooker',
      'pressure cook': 'Pressure Cooker',
      
      // Roasting variations
      'roasting': 'Roasted',
      'roasted': 'Roasted',
      
      // Boiling variations
      'boiled': 'Boiled',
      'boiling': 'Boiled',
      'simmering': 'Boiled',
      'simmer': 'Boiled',
      
      // Air frying variations
      'air fried': 'Air Fried',
      'air fryer': 'Air Fried',
      
      // Stovetop variations
      'stovetop': 'Stovetop',
      'oven': 'Stovetop',
      'cooking': 'Stovetop',
      
      // Skillet variations
      'skillet': 'Skillet',
      'pan searing': 'Skillet',
      'pan seared': 'Skillet',
      'searing': 'Skillet',
      
      // Frying variations
      'fried': 'Fried',
      'frying': 'Fried',
      'deep frying': 'Fried',
      'deep fried': 'Fried',
      'deep-frying': 'Fried',
      'deep-fried': 'Fried',
      
      // Steaming variations
      'steamed': 'Steamed',
      'steaming': 'Steamed',
      
      // Smoking variations
      'smoked': 'Smoked',
      'smoking': 'Smoked',
      
      // Braising variations
      'braised': 'Braised',
      'braising': 'Braised',
      
      // Broiling variations
      'broiling': 'Pan-fried', // Map to closest equivalent
      'broil': 'Pan-fried',
      
      // Microwave
      'microwaving': 'Stovetop', // Map to closest equivalent
      'microwave': 'Stovetop',
      
      // Toasting
      'toasting': 'Baked', // Map to closest equivalent
      
      // Special cases
      'marinade': 'Grilled',
      'marinating': 'Grilled',
      'blanching': 'Boiled',
      'emulsifying': 'No Cook',
      'massaging': 'No Cook',
      'slicing': 'No Cook',
      'skewering': 'Grilled',
    };
    
    // First try exact match (lowercase)
    const normalized = prepMethodMapping[firstMethod.toLowerCase()];
    if (normalized) return normalized;
    
    // If no exact match, try to match the form option by capitalizing first letter
    const formOptions = ['Air Fried', 'Baked', 'Boiled', 'Braised', 'Fried', 'Grilled', 
                        'No Cook', 'Pan-fried', 'Roasted', 'Sautéed', 'Skillet', 
                        'Slow Cooked', 'Steamed', 'Stir-fried', 'Stovetop', 'Smoked'];
    
    const methodLower = firstMethod.toLowerCase();
    const matchingOption = formOptions.find(opt => opt.toLowerCase() === methodLower);
    
    return matchingOption || firstMethod;
  };

  // Parse protein_type if it's stored as JSON string array
  const parseProteinType = (proteinType: any): string => {
    if (!proteinType) return '';
    
    let primaryProtein = '';
    
    // If it's already a string, use it
    if (typeof proteinType === 'string') {
      // Check if it's a JSON array string like '["beef"]' or '[]'
      if (proteinType.startsWith('[') && proteinType.endsWith(']')) {
        try {
          const parsed = JSON.parse(proteinType);
          if (Array.isArray(parsed) && parsed.length > 0) {
            primaryProtein = parsed[0];
          }
        } catch {
          return proteinType;
        }
      } else {
        primaryProtein = proteinType;
      }
    }
    
    // If it's an array, take the first element
    if (Array.isArray(proteinType)) {
      primaryProtein = proteinType.length > 0 ? proteinType[0] : '';
    }
    
    // Normalize protein types to match form options
    const proteinMapping: { [key: string]: string } = {
      'bacon': 'pork',
      'ham': 'pork', 
      'sausage': 'pork',
      'shrimp': 'seafood',
      'salmon': 'fish',
      'egg': 'eggs',
      'vegan': 'vegan',
      'vegetarian': 'vegetarian',
    };
    
    // Return mapped value or original
    return proteinMapping[primaryProtein.toLowerCase()] || primaryProtein;
  };

  const [formData, setFormData] = useState({
    name: meal?.name || '',
    description: meal?.description || '',
    ingredients: normalizeIngredients(meal?.ingredients || []),
    ingredient_sections: (meal?.ingredient_sections || []) as any[],
    instructions: (meal?.instructions || []) as string[],
    prepTimeMin: meal?.prepTimeMin || 0,
    prepTimeHour: meal?.prepTimeHour || 0,
    cookTimeMin: meal?.cookTimeMin || 0,
    cookTimeHour: meal?.cookTimeHour || 0,
    servingSize: meal?.servingSize || 1,
    course: meal?.course || '',
    prep_method: normalizePrepMethod(meal?.prep_method),
    cuisine_type: meal?.cuisine_type || '',
    image: meal?.image || '',
    source_url: meal?.source_url || '',
    author: meal?.author || '',
    dietary_tags: meal?.dietary_tags || [],
    allergen_contains: meal?.allergen_contains || [],
    protein_type: parseProteinType(meal?.protein_type),
    required_equipment: (meal?.required_equipment || []).map(eq => 
      eq.toLowerCase().replace(/\s+/g, '_').replace('dutch_oven', 'dutch_oven')
    ),
    meal_tags: meal?.meal_tags || [],
    calories: meal?.calories || '',
    protein: meal?.protein || '',
    carbs: meal?.carbs || '',
    fats: meal?.fats || '',
    status: meal?.status || 'active',
  });

  const createMeal = useCreateMeal();
  const updateMeal = useUpdateMeal();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Capitalize units when saving back to database
    const capitalizeIngredients = (ingredients: Ingredient[]) => {
      // Reverse mapping from form units to database units
      const reverseUnitMapping: { [key: string]: string } = {
        'oz': 'Ounce',
        'tbs': 'Tablespoon',
        'tsp': 'Teaspoon',
        'lb': 'Pound',
        'lbs': 'Pounds',
        'fl oz': 'Fluid Ounce',
      };

      return ingredients.map(ing => {
        const dbUnit = reverseUnitMapping[ing.unit] || 
          (ing.unit ? ing.unit.charAt(0).toUpperCase() + ing.unit.slice(1) : '');
        
        return {
          ...ing,
          unit: dbUnit,
          // Ensure ingredient_id is used for database storage (handle both formats)
          ingredient_id: ing.ingredient_id || (ing as any).ingredientId,
        };
      });
    };

    // Convert equipment back to database format (e.g., "dutch_oven" -> "Dutch oven")
    const formatEquipmentForDb = (equipment: string[]): string[] => {
      return equipment.map(eq => {
        // Special cases
        if (eq === 'dutch_oven') return 'Dutch oven';
        if (eq === 'cast_iron_skillet') return 'cast iron skillet';
        if (eq === 'baking_sheet') return 'baking sheet';
        if (eq === 'baking_dish') return 'baking dish';
        if (eq === 'mixing_bowl') return 'mixing bowl';
        if (eq === 'loaf_pan') return 'loaf pan';
        if (eq === 'muffin_tin') return 'muffin tin';
        if (eq === 'pizza_stone') return 'pizza stone';
        if (eq === 'pressure_cooker') return 'pressure cooker';
        if (eq === 'instant_pot') return 'Instant Pot';
        if (eq === 'rice_cooker') return 'rice cooker';
        if (eq === 'slow_cooker') return 'slow cooker';
        if (eq === 'food_processor') return 'food processor';
        if (eq === 'hand_mixer') return 'hand mixer';
        if (eq === 'stand_mixer') return 'stand mixer';
        if (eq === 'stock_pot') return 'stock pot';
        if (eq === 'toaster_oven') return 'toaster oven';
        if (eq === 'sous_vide') return 'sous vide';
        if (eq === 'springform_pan') return 'springform pan';
        if (eq === 'air_fryer') return 'air fryer';
        
        // Default: just replace underscores with spaces
        return eq.replace(/_/g, ' ');
      });
    };

    const mealData = {
      ...formData,
      ingredients:
        formData.ingredients.length > 0 
          ? capitalizeIngredients(formData.ingredients) 
          : null,
      ingredient_sections:
        formData.ingredient_sections.length > 0
          ? formData.ingredient_sections
          : null,
      instructions:
        formData.instructions.length > 0 ? formData.instructions : null,
      prepTimeMin: Number(formData.prepTimeMin),
      prepTimeHour: Number(formData.prepTimeHour),
      cookTimeMin: Number(formData.cookTimeMin),
      cookTimeHour: Number(formData.cookTimeHour),
      servingSize: Number(formData.servingSize),
      required_equipment: formatEquipmentForDb(formData.required_equipment),
      calories: formData.calories ? Number(formData.calories) : undefined,
      protein: formData.protein ? Number(formData.protein) : undefined,
      carbs: formData.carbs ? Number(formData.carbs) : undefined,
      fats: formData.fats ? Number(formData.fats) : undefined,
    };

    try {
      if (meal) {
        await updateMeal.mutateAsync({ id: meal.id, ...mealData });
      } else {
        await createMeal.mutateAsync(mealData);
      }
      onSuccess?.();
    } catch (error) {
      console.error('Error saving meal:', error);
      onError?.();
    }
  };

  const isLoading = createMeal.isPending || updateMeal.isPending;

  const updateFormData = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <div className="max-h-[85vh] overflow-y-auto bg-slate-900">
      <form id="meal-form" onSubmit={handleSubmit} className="space-y-8 p-6">
        {/* Basic Information */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
            📝 Basic Information
          </h3>

          <div className="space-y-6">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Recipe Name *
              </label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateFormData('name', e.target.value)}
                required
                placeholder="Enter meal name"
                className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-slate-300 mb-2">
                Recipe Image
              </label>
              <ImageUpload
                value={formData.image}
                onChange={(url) => updateFormData('image', url)}
                bucket="meals"
              />
            </div>
          </div>
        </div>

        {/* Timing & Servings */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
            ⏱️ Timing & Servings
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-blue-500/10 border border-blue-500/20 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-400 mb-3">Prep Time</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label
                    htmlFor="prepTimeHour"
                    className="block text-xs font-medium text-blue-300 mb-1"
                  >
                    Hours
                  </label>
                  <Input
                    id="prepTimeHour"
                    type="number"
                    value={formData.prepTimeHour}
                    onChange={(e) =>
                      updateFormData('prepTimeHour', Number(e.target.value))
                    }
                    min="0"
                    className="text-sm bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="prepTimeMin"
                    className="block text-xs font-medium text-blue-300 mb-1"
                  >
                    Minutes
                  </label>
                  <Input
                    id="prepTimeMin"
                    type="number"
                    value={formData.prepTimeMin}
                    onChange={(e) =>
                      updateFormData('prepTimeMin', Number(e.target.value))
                    }
                    min="0"
                    className="text-sm bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
              </div>
            </div>

            <div className="bg-orange-500/10 border border-orange-500/20 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-400 mb-3">Cook Time</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label
                    htmlFor="cookTimeHour"
                    className="block text-xs font-medium text-orange-300 mb-1"
                  >
                    Hours
                  </label>
                  <Input
                    id="cookTimeHour"
                    type="number"
                    value={formData.cookTimeHour}
                    onChange={(e) =>
                      updateFormData('cookTimeHour', Number(e.target.value))
                    }
                    min="0"
                    className="text-sm bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="cookTimeMin"
                    className="block text-xs font-medium text-orange-300 mb-1"
                  >
                    Minutes
                  </label>
                  <Input
                    id="cookTimeMin"
                    type="number"
                    value={formData.cookTimeMin}
                    onChange={(e) =>
                      updateFormData('cookTimeMin', Number(e.target.value))
                    }
                    min="0"
                    className="text-sm bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
              </div>
            </div>

            <div className="bg-green-500/10 border border-green-500/20 p-4 rounded-lg">
              <h4 className="font-semibold text-green-400 mb-3">Servings</h4>
              <div>
                <label
                  htmlFor="servingSize"
                  className="block text-xs font-medium text-green-300 mb-1"
                >
                  Number of Servings
                </label>
                <Input
                  id="servingSize"
                  type="number"
                  value={formData.servingSize}
                  onChange={(e) =>
                    updateFormData('servingSize', Number(e.target.value))
                  }
                  min="1"
                  className="text-sm bg-slate-700/50 border-slate-600 text-white"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Recipe Details */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
            👨‍🍳 Recipe Details
          </h3>

          <div className="space-y-8">
            <GroupedIngredientsForm
              ingredients={formData.ingredients}
              ingredientSections={formData.ingredient_sections}
              onChange={(ingredients) =>
                updateFormData('ingredients', ingredients)
              }
              onSectionsChange={(sections) =>
                updateFormData('ingredient_sections', sections)
              }
            />

            <InstructionsForm
              instructions={formData.instructions}
              onChange={(instructions) =>
                updateFormData('instructions', instructions)
              }
            />
          </div>
        </div>

        {/* Categories & Tags */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
            🏷️ Categories & Tags
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div>
              <label
                htmlFor="course"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Course
              </label>
              <Select
                id="course"
                value={formData.course}
                onChange={(e) => updateFormData('course', e.target.value)}
                options={COURSE_OPTIONS}
                placeholder="Select course"
                allowEmpty
              />
            </div>

            <div>
              <label
                htmlFor="prep_method"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Prep Method
              </label>
              <Select
                id="prep_method"
                value={formData.prep_method}
                onChange={(e) => updateFormData('prep_method', e.target.value)}
                options={PREP_METHOD_OPTIONS}
                placeholder="Select prep method"
                allowEmpty
              />
            </div>

            <div>
              <label
                htmlFor="cuisine_type"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Cuisine Type
              </label>
              <Select
                id="cuisine_type"
                value={formData.cuisine_type}
                onChange={(e) => updateFormData('cuisine_type', e.target.value)}
                options={CUISINE_OPTIONS}
                placeholder="Select cuisine type"
                allowEmpty
              />
            </div>

            <div>
              <label
                htmlFor="protein_type"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Protein Type
              </label>
              <Select
                id="protein_type"
                value={formData.protein_type}
                onChange={(e) => updateFormData('protein_type', e.target.value)}
                options={PROTEIN_OPTIONS}
                placeholder="Select protein type"
                allowEmpty
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="dietary_tags"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Dietary Tags
              </label>
              <MultiSelect
                options={DIETARY_TAGS_OPTIONS}
                value={formData.dietary_tags}
                onChange={(value) => updateFormData('dietary_tags', value)}
                placeholder="Select dietary tags"
              />
            </div>

            <div>
              <label
                htmlFor="allergen_contains"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Allergens
              </label>
              <MultiSelect
                options={ALLERGEN_OPTIONS}
                value={formData.allergen_contains}
                onChange={(value) => updateFormData('allergen_contains', value)}
                placeholder="Select allergens"
              />
            </div>

            <div>
              <label
                htmlFor="required_equipment"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Required Equipment
              </label>
              <MultiSelect
                options={EQUIPMENT_OPTIONS}
                value={formData.required_equipment}
                onChange={(value) =>
                  updateFormData('required_equipment', value)
                }
                placeholder="Select required equipment"
              />
            </div>

            <div>
              <label
                htmlFor="meal_tags"
                className="block text-sm font-semibold text-slate-300 mb-2"
              >
                Meal Tags
              </label>
              <MultiSelect
                options={MEAL_TAGS_OPTIONS}
                value={formData.meal_tags}
                onChange={(value) => updateFormData('meal_tags', value)}
                placeholder="Select meal tags"
              />
            </div>
          </div>
        </div>

        {/* Nutrition & Source */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Nutrition */}
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
              🥗 Nutrition (per serving)
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="calories"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Calories
                </label>
                <Input
                  id="calories"
                  type="number"
                  value={formData.calories}
                  onChange={(e) => updateFormData('calories', e.target.value)}
                  min="0"
                  placeholder="250"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div>
                <label
                  htmlFor="protein"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Protein (g)
                </label>
                <Input
                  id="protein"
                  type="number"
                  value={formData.protein}
                  onChange={(e) => updateFormData('protein', e.target.value)}
                  min="0"
                  placeholder="20"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div>
                <label
                  htmlFor="carbs"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Carbs (g)
                </label>
                <Input
                  id="carbs"
                  type="number"
                  value={formData.carbs}
                  onChange={(e) => updateFormData('carbs', e.target.value)}
                  min="0"
                  placeholder="30"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div>
                <label
                  htmlFor="fats"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Fats (g)
                </label>
                <Input
                  id="fats"
                  type="number"
                  value={formData.fats}
                  onChange={(e) => updateFormData('fats', e.target.value)}
                  min="0"
                  placeholder="15"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
            </div>
          </div>

          {/* Source Information */}
          <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
            <h3 className="text-xl font-bold text-white mb-6 border-b border-slate-700 pb-3">
              📚 Source Information
            </h3>

            <div className="space-y-4">
              <div>
                <label
                  htmlFor="author"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Author
                </label>
                <Input
                  id="author"
                  value={formData.author}
                  onChange={(e) => updateFormData('author', e.target.value)}
                  placeholder="Recipe author or source"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>

              <div>
                <label
                  htmlFor="source_url"
                  className="block text-sm font-semibold text-slate-300 mb-2"
                >
                  Source URL
                </label>
                <Input
                  id="source_url"
                  type="url"
                  value={formData.source_url}
                  onChange={(e) => updateFormData('source_url', e.target.value)}
                  placeholder="https://example.com/recipe"
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-slate-800/50 rounded-xl border border-slate-700 p-6">
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="px-6 py-2 bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:text-white transition-all"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-lg shadow-blue-600/20 disabled:opacity-50 disabled:shadow-none transition-all"
            >
              {isLoading
                ? 'Saving...'
                : meal
                  ? 'Update Recipe'
                  : 'Create Recipe'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

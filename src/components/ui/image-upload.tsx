'use client';

import { useState, useCallback } from 'react';
import Image from 'next/image';
import { supabase } from '@/lib/supabase';
import { Button } from './button';
import { Upload, X, ImageIcon } from 'lucide-react';

interface ImageUploadProps {
  value?: string;
  onChange: (url: string | null) => void;
  bucket?: string;
  disabled?: boolean;
}

export function ImageUpload({
  value,
  onChange,
  bucket = 'meals',
  disabled,
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const uploadImage = useCallback(
    async (file: File) => {
      if (!file) return;

      setUploading(true);
      try {
        // Create unique filename
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${fileName}`;

        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from(bucket)
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false,
          });

        if (error) {
          console.error('Upload error:', error);
          throw error;
        }

        // Get public URL
        const {
          data: { publicUrl },
        } = supabase.storage.from(bucket).getPublicUrl(data.path);

        onChange(publicUrl);
      } catch (error) {
        console.error('Error uploading image:', error);
        alert('Error uploading image. Please try again.');
      } finally {
        setUploading(false);
      }
    },
    [bucket, onChange]
  );

  const handleFileSelect = useCallback(
    (file: File) => {
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        alert('File size must be less than 5MB');
        return;
      }

      uploadImage(file);
    },
    [uploadImage]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const removeImage = useCallback(() => {
    onChange(null);
  }, [onChange]);

  // Check if value is a valid URL
  const isValidUrl = (string: string) => {
    if (!string || typeof string !== 'string' || string.trim() === '') {
      return false;
    }
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
      return false;
    }
  };

  if (value && isValidUrl(value)) {
    return (
      <div className="space-y-2">
        <div className="relative inline-block">
          <Image
            src={value}
            alt="Uploaded meal image"
            width={500}
            height={500}
            className="w-[500px] h-[500px] object-cover rounded-lg border border-gray-300"
            onError={(e) => {
              console.error('Image failed to load:', value);
              onChange(null); // Clear invalid image
            }}
          />
          {!disabled && (
            <button
              type="button"
              onClick={removeImage}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        {!disabled && (
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileInput}
              className="hidden"
              id="replace-image"
              disabled={uploading}
            />
            <label htmlFor="replace-image">
              <Button
                type="button"
                variant="outline"
                size="sm"
                disabled={uploading}
                className="cursor-pointer"
              >
                Replace Image
              </Button>
            </label>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`
        border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
        ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={() =>
        !disabled && document.getElementById('file-input')?.click()
      }
    >
      <input
        type="file"
        accept="image/*"
        onChange={handleFileInput}
        className="hidden"
        id="file-input"
        disabled={disabled || uploading}
      />

      <div className="space-y-2">
        {uploading ? (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        ) : (
          <ImageIcon className="w-8 h-8 text-gray-400 mx-auto" />
        )}

        <div>
          <p className="text-sm font-medium text-gray-700">
            {uploading ? 'Uploading...' : 'Drop image here or click to upload'}
          </p>
          <p className="text-xs text-gray-500">PNG, JPG, WEBP up to 5MB</p>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Trash2, Plus } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';

interface Ingredient {
  name: string;
  amount: number | null;
  unit: string;
  emoji: string;
  ingredient_id?: string; // Add this to track the master ingredient ID
}

interface MasterIngredient {
  id: string;
  name: string;
  emoji: string | null;
  category: string | null;
}

interface IngredientsFormProps {
  ingredients: Ingredient[];
  onChange: (ingredients: Ingredient[]) => void;
}

const UNIT_OPTIONS = [
  'bag',
  'bottle',
  'box',
  'bunch',
  'can',
  'clove',
  'cloves',
  'cup',
  'cups',
  'dash',
  'fl oz',
  'g',
  'gallon',
  'head',
  'item',
  'jar',
  'kg',
  'l',
  'large',
  'lb',
  'lbs',
  'medium',
  'ml',
  'oz',
  'package',
  'piece',
  'pieces',
  'pinch',
  'pint',
  'pound',
  'pounds',
  'quart',
  'slice',
  'slices',
  'tbs',
  'tsp',
  'whole',
];

// Optimized ingredient selector component
function IngredientSelector({
  index,
  ingredient,
  isOpen,
  onOpenChange,
  onSelect,
}: {
  index: number;
  ingredient: Ingredient;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (ingredient: MasterIngredient) => void;
}) {
  const [searchTerm, setSearchTerm] = useState('');

  // Search ingredients with debouncing
  const { data: searchResults = [], isLoading } = useQuery({
    queryKey: ['ingredients-search', searchTerm],
    queryFn: async () => {
      if (!searchTerm || searchTerm.length < 2) {
        return [];
      }

      const { data, error } = await supabase
        .from('ingredients')
        .select('*')
        .ilike('name', `%${searchTerm}%`)
        .order('name', { ascending: true })
        .limit(50); // Limit to 50 results for performance

      if (error) throw error;
      return data as MasterIngredient[];
    },
    enabled: !!searchTerm && searchTerm.length >= 2,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  // Reset search when popover closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
    }
  }, [isOpen]);

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger className="flex h-10 w-full items-center justify-between rounded-md border border-slate-600 bg-slate-700/50 px-3 py-2 text-sm text-white placeholder:text-slate-400 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900 disabled:cursor-not-allowed disabled:opacity-50 transition-colors">
        <div className="flex items-center gap-2">
          {ingredient.emoji && (
            <span className="text-lg">{ingredient.emoji}</span>
          )}
          <span className={cn(!ingredient.name && 'text-slate-400')}>
            {ingredient.name || 'Search for ingredient...'}
          </span>
        </div>
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0 bg-slate-800 border-slate-700">
        <Command className="bg-slate-800">
          <CommandInput
            placeholder="Type to search ingredients (min 2 characters)..."
            className="border-slate-700"
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandEmpty>
            <div className="p-4 text-center">
              {searchTerm.length < 2 ? (
                <div>
                  <p className="text-sm text-slate-400">
                    Start typing to search ingredients
                  </p>
                  <p className="text-xs text-slate-500 mt-1">
                    Type at least 2 characters to see results
                  </p>
                </div>
              ) : isLoading ? (
                <div>
                  <p className="text-sm text-slate-400">Searching...</p>
                </div>
              ) : (
                <div>
                  <p className="text-sm text-slate-400">
                    No ingredients found for &quot;{searchTerm}&quot;
                  </p>
                  <p className="text-xs text-slate-500 mt-1">
                    Contact admin to add new ingredients to the master list.
                  </p>
                </div>
              )}
            </div>
          </CommandEmpty>
          <CommandGroup className="max-h-[300px] overflow-y-auto">
            {searchResults.map((masterIngredient) => (
              <CommandItem
                key={masterIngredient.id}
                value={masterIngredient.name}
                onSelect={() => onSelect(masterIngredient)}
                className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    ingredient.name === masterIngredient.name
                      ? 'opacity-100'
                      : 'opacity-0'
                  )}
                />
                <span className="mr-2 text-lg">
                  {masterIngredient.emoji || '🥗'}
                </span>
                <span className="flex-1">{masterIngredient.name}</span>
                {masterIngredient.category && (
                  <span className="ml-auto text-xs text-slate-500">
                    {masterIngredient.category}
                  </span>
                )}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export function IngredientsForm({
  ingredients,
  onChange,
}: IngredientsFormProps) {
  const [openIngredientPopover, setOpenIngredientPopover] = useState<
    number | null
  >(null);

  const updateIngredient = (
    index: number,
    field: keyof Ingredient,
    value: string | number | null
  ) => {
    const newIngredients = [...ingredients];
    newIngredients[index] = { ...newIngredients[index], [field]: value };
    onChange(newIngredients);
  };

  const selectMasterIngredient = (
    index: number,
    masterIngredient: MasterIngredient
  ) => {
    const newIngredients = [...ingredients];
    newIngredients[index] = {
      ...newIngredients[index],
      name: masterIngredient.name,
      emoji: masterIngredient.emoji || '🥗',
      ingredient_id: masterIngredient.id, // Store the master ingredient ID
    };
    onChange(newIngredients);
  };

  const addIngredient = () => {
    onChange([
      ...ingredients,
      { name: '', amount: null, unit: '', emoji: '🥗' },
    ]);
  };

  const removeIngredient = (index: number) => {
    onChange(ingredients.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <label className="text-base font-medium text-white">Ingredients</label>
      </div>

      {ingredients.map((ingredient, index) => (
        <div
          key={index}
          className="grid grid-cols-12 gap-2 items-end p-4 bg-slate-800/50 border border-slate-700 rounded-lg"
        >
          {/* Amount */}
          <div className="col-span-2">
            <label className="text-sm font-medium text-slate-300">Amount</label>
            <Input
              type="number"
              step="any"
              min="0"
              value={ingredient.amount === 0 || ingredient.amount === null ? '' : ingredient.amount}
              onChange={(e) => {
                const value = e.target.value;
                updateIngredient(
                  index,
                  'amount',
                  value === '' ? null : parseFloat(value) || 0
                )
              }}
              placeholder=""
              className="bg-slate-700/50 border-slate-600 text-white"
            />
          </div>

          {/* Unit */}
          <div className="col-span-2">
            <label className="text-sm font-medium text-slate-300">Unit</label>
            <Select
              value={ingredient.unit}
              onChange={(e) => updateIngredient(index, 'unit', e.target.value)}
              options={UNIT_OPTIONS}
              placeholder="Select unit"
              allowEmpty
            />
          </div>

          {/* Master Ingredient Selector */}
          <div className="col-span-7">
            <label className="text-sm font-medium text-slate-300">
              Ingredient
            </label>
            <IngredientSelector
              index={index}
              ingredient={ingredient}
              isOpen={openIngredientPopover === index}
              onOpenChange={(open) =>
                setOpenIngredientPopover(open ? index : null)
              }
              onSelect={(masterIngredient) => {
                selectMasterIngredient(index, masterIngredient);
                setOpenIngredientPopover(null);
              }}
            />
          </div>

          {/* Remove Button */}
          <div className="col-span-1">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => removeIngredient(index)}
              className="w-full h-10 p-2 bg-red-600/20 border-red-500/50 text-red-400 hover:bg-red-600/30 hover:border-red-500 transition-all"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}

      {ingredients.length === 0 && (
        <div className="text-center py-8 text-slate-400 border-2 border-dashed border-slate-700 rounded-lg bg-slate-800/20">
          No ingredients added yet. Click &quot;Add Ingredient&quot; to get
          started.
        </div>
      )}

      <div className="flex justify-center">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addIngredient}
          className="flex items-center gap-2 bg-blue-600/20 border-blue-500/50 text-blue-400 hover:bg-blue-600/30 hover:border-blue-500 transition-all"
        >
          <Plus className="h-4 w-4" />
          Add Ingredient
        </Button>
      </div>
    </div>
  );
}

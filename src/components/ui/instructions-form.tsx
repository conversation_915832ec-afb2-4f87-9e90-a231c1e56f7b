'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
// Using HTML label elements instead of component
import { Trash2, Plus, GripVertical } from 'lucide-react';

interface InstructionsFormProps {
  instructions: string[];
  onChange: (instructions: string[]) => void;
}

export function InstructionsForm({
  instructions,
  onChange,
}: InstructionsFormProps) {
  const updateInstruction = (index: number, value: string) => {
    const newInstructions = [...instructions];
    newInstructions[index] = value;
    onChange(newInstructions);
  };

  const addInstruction = () => {
    onChange([...instructions, '']);
  };

  const removeInstruction = (index: number) => {
    onChange(instructions.filter((_, i) => i !== index));
  };

  const moveInstruction = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= instructions.length) return;

    const newInstructions = [...instructions];
    const [moved] = newInstructions.splice(fromIndex, 1);
    newInstructions.splice(toIndex, 0, moved);
    onChange(newInstructions);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <label className="text-base font-medium text-white">
          Cooking Instructions
        </label>
      </div>

      {instructions.map((instruction, index) => (
        <div key={index} className="flex gap-2 items-start">
          {/* Step Number */}
          <div className="flex flex-col items-center gap-1 pt-2">
            <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
              {index + 1}
            </div>
            {instructions.length > 1 && (
              <div className="flex flex-col gap-1">
                {index > 0 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => moveInstruction(index, index - 1)}
                    className="h-6 w-6 p-1 text-slate-400 hover:text-white hover:bg-slate-700"
                  >
                    ↑
                  </Button>
                )}
                {index < instructions.length - 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => moveInstruction(index, index + 1)}
                    className="h-6 w-6 p-1 text-slate-400 hover:text-white hover:bg-slate-700"
                  >
                    ↓
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Instruction Text */}
          <div className="flex-1">
            <Textarea
              value={instruction}
              onChange={(e) => updateInstruction(index, e.target.value)}
              placeholder={`Step ${index + 1}: Describe what to do...`}
              className="min-h-[80px] resize-none bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
            />
          </div>

          {/* Remove Button */}
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => removeInstruction(index)}
            className="mt-2 h-10 w-10 p-2 bg-red-600/20 border-red-500/50 text-red-400 hover:bg-red-600/30 hover:border-red-500 transition-all"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ))}

      {instructions.length === 0 && (
        <div className="text-center py-8 text-slate-400 border-2 border-dashed border-slate-700 rounded-lg bg-slate-800/20">
          No cooking steps added yet. Click &quot;Add Step&quot; to get started.
        </div>
      )}

      <div className="flex justify-center">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addInstruction}
          className="flex items-center gap-2 bg-blue-600/20 border-blue-500/50 text-blue-400 hover:bg-blue-600/30 hover:border-blue-500 transition-all"
        >
          <Plus className="h-4 w-4" />
          Add Step
        </Button>
      </div>

      {instructions.length > 0 && (
        <div className="text-sm text-slate-400 mt-4 p-3 bg-slate-800/30 border border-slate-700 rounded-lg">
          💡 <strong className="text-slate-300">Tip:</strong> Write clear,
          step-by-step instructions. Use the up/down arrows to reorder steps.
        </div>
      )}
    </div>
  );
}

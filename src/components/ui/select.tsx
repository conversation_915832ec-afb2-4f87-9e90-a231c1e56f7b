'use client';

import * as React from 'react';

export interface SelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {
  options?: { value: string; label: string }[] | string[];
  placeholder?: string;
  allowEmpty?: boolean;
}

const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    { className = '', options = [], placeholder, allowEmpty = false, ...props },
    ref
  ) => {
    const normalizedOptions = options.map((option) =>
      typeof option === 'string' ? { value: option, label: option } : option
    );

    return (
      <select
        className={`flex h-10 w-full rounded-md border border-slate-600 bg-slate-700/50 px-3 py-2 text-sm text-white ring-offset-slate-900 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 hover:bg-slate-700 transition-colors ${className}`}
        ref={ref}
        {...props}
      >
        {(placeholder || allowEmpty) && (
          <option value="">{placeholder || 'Select an option...'}</option>
        )}
        {normalizedOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  }
);
Select.displayName = 'Select';

export { Select };

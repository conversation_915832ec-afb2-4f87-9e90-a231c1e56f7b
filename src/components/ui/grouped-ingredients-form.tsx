'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { Trash2, Plus, GripVertical, FolderPlus, X } from 'lucide-react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface Ingredient {
  name: string;
  amount: number | null;
  unit: string;
  emoji: string;
  ingredient_id?: string;
}

interface IngredientGroup {
  id: string;
  title: string;
  ingredients: Ingredient[];
}

interface MasterIngredient {
  id: string;
  name: string;
  emoji: string | null;
  category: string | null;
}

interface GroupedIngredientsFormProps {
  ingredients: Ingredient[];
  ingredientSections?: IngredientGroup[];
  onChange: (ingredients: Ingredient[]) => void;
  onSectionsChange?: (sections: IngredientGroup[]) => void;
}

const UNIT_OPTIONS = [
  'bag',
  'bottle',
  'box',
  'bunch',
  'can',
  'clove',
  'cloves',
  'cup',
  'cups',
  'dash',
  'fl oz',
  'g',
  'gallon',
  'head',
  'item',
  'jar',
  'kg',
  'l',
  'large',
  'lb',
  'lbs',
  'medium',
  'ml',
  'oz',
  'package',
  'packet',
  'piece',
  'pieces',
  'pinch',
  'pint',
  'pound',
  'pounds',
  'quart',
  'rib',
  'ribs',
  'slice',
  'slices',
  'small',
  'stalk',
  'stalks',
  'tbs',
  'tsp',
  'whole',
];

// Custom ingredient selector with proper keyboard navigation
function IngredientSelector({
  groupId,
  index,
  ingredient,
  isOpen,
  onOpenChange,
  onSelect,
}: {
  groupId: string;
  index: number;
  ingredient: Ingredient;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (ingredient: MasterIngredient) => void;
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const searchInputRef = React.useRef<HTMLInputElement>(null);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Search ingredients with debouncing
  const { data: searchResults = [], isLoading } = useQuery({
    queryKey: ['ingredients-search', searchTerm],
    queryFn: async () => {
      if (!searchTerm || searchTerm.length < 2) {
        return [];
      }

      const { data, error } = await supabase
        .from('ingredients')
        .select('*')
        .ilike('name', `%${searchTerm}%`)
        .order('name', { ascending: true })
        .limit(50);

      if (error) throw error;
      return data as MasterIngredient[];
    },
    enabled: !!searchTerm && searchTerm.length >= 2,
    staleTime: 5 * 60 * 1000,
  });

  // Reset state when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setSelectedIndex(-1);
    } else {
      // Focus search input when dropdown opens
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Reset selected index when search results change
  useEffect(() => {
    setSelectedIndex(-1);
  }, [searchResults]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onOpenChange(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onOpenChange]);

  const handleTriggerKeyDown = (e: React.KeyboardEvent) => {
    // Auto-open on typing when trigger is focused
    if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
      e.preventDefault();
      onOpenChange(true);
      setTimeout(() => {
        setSearchTerm(e.key);
      }, 10);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      onOpenChange(true);
    }
  };

  const handleItemClick = (masterIngredient: MasterIngredient) => {
    onSelect(masterIngredient);
    onOpenChange(false);
  };

  return (
    <div className="relative">
      {/* Input Trigger */}
      <div
        ref={triggerRef}
        tabIndex={0}
        className="flex h-10 w-full items-center justify-between rounded-md border border-slate-600 bg-slate-700/50 px-3 py-2 text-sm text-white placeholder:text-slate-400 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900 transition-colors cursor-pointer"
        onClick={() => onOpenChange(true)}
        onKeyDown={handleTriggerKeyDown}
        onFocus={() => onOpenChange(true)}
      >
        <div className="flex items-center gap-2 flex-1">
          {ingredient.emoji && (
            <span className="text-lg">{ingredient.emoji}</span>
          )}
          <span className={cn(!ingredient.name && 'text-slate-400')}>
            {ingredient.name || 'Search for ingredient...'}
          </span>
        </div>
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-slate-800 border border-slate-700 rounded-md shadow-lg max-h-[300px] overflow-y-auto"
        >
          {/* Search Input */}
          <div className="p-2 border-b border-slate-700">
            <Input
              ref={searchInputRef}
              placeholder="Type to search ingredients (min 2 characters)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                // Handle navigation when search input is focused
                switch (e.key) {
                  case 'ArrowDown':
                    e.preventDefault();
                    if (searchResults.length > 0) {
                      setSelectedIndex((prev) =>
                        prev < searchResults.length - 1 ? prev + 1 : 0
                      );
                    }
                    break;
                  case 'ArrowUp':
                    e.preventDefault();
                    if (searchResults.length > 0) {
                      setSelectedIndex((prev) =>
                        prev > 0 ? prev - 1 : searchResults.length - 1
                      );
                    }
                    break;
                  case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && searchResults[selectedIndex]) {
                      onSelect(searchResults[selectedIndex]);
                      onOpenChange(false);
                    }
                    break;
                  case 'Escape':
                    e.preventDefault();
                    onOpenChange(false);
                    break;
                }
              }}
              className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
              autoFocus
            />
          </div>

          {/* Results */}
          <div className="py-1">
            {searchTerm.length < 2 ? (
              <div className="p-4 text-center">
                <p className="text-sm text-slate-400">
                  Start typing to search ingredients
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  Type at least 2 characters to see results
                </p>
              </div>
            ) : isLoading ? (
              <div className="p-4 text-center">
                <p className="text-sm text-slate-400">Searching...</p>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="p-4 text-center">
                <p className="text-sm text-slate-400">
                  No ingredients found for &quot;{searchTerm}&quot;
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  Contact admin to add new ingredients to the master list.
                </p>
              </div>
            ) : (
              searchResults.map((masterIngredient, idx) => (
                <div
                  key={masterIngredient.id}
                  className={cn(
                    'flex items-center px-3 py-2 cursor-pointer text-slate-300 hover:bg-slate-700 hover:text-white transition-colors',
                    idx === selectedIndex && 'bg-slate-700 text-white'
                  )}
                  onClick={() => handleItemClick(masterIngredient)}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      ingredient.name === masterIngredient.name
                        ? 'opacity-100'
                        : 'opacity-0'
                    )}
                  />
                  <span className="mr-2 text-lg">
                    {masterIngredient.emoji || '🥗'}
                  </span>
                  <span className="flex-1">{masterIngredient.name}</span>
                  {masterIngredient.category && (
                    <span className="ml-auto text-xs text-slate-500">
                      {masterIngredient.category}
                    </span>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Sortable Ingredient Item Component
function SortableIngredient({
  ingredient,
  index,
  groupId,
  updateIngredient,
  removeIngredient,
  selectMasterIngredient,
  openIngredientPopover,
  setOpenIngredientPopover,
}: {
  ingredient: Ingredient;
  index: number;
  groupId: string;
  updateIngredient: (groupId: string, index: number, field: keyof Ingredient, value: string | number | null) => void;
  removeIngredient: (groupId: string, index: number) => void;
  selectMasterIngredient: (groupId: string, index: number, masterIngredient: MasterIngredient) => void;
  openIngredientPopover: string | null;
  setOpenIngredientPopover: (value: string | null) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `${groupId}-${index}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "grid grid-cols-12 gap-2 items-end p-3 bg-slate-800/50 border border-slate-700 rounded-lg",
        isDragging && "opacity-50 z-50"
      )}
    >
      {/* Drag Handle */}
      <div className="col-span-1 flex justify-center">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-slate-700 rounded"
        >
          <GripVertical className="h-4 w-4 text-slate-500" />
        </div>
      </div>

      {/* Amount */}
      <div className="col-span-2">
        <label className="text-xs font-medium text-slate-300">
          Amount
        </label>
        <Input
          type="number"
          step="any"
          min="0"
          value={ingredient.amount === 0 || ingredient.amount === null ? '' : ingredient.amount}
          onChange={(e) => {
            const value = e.target.value;
            // Allow empty string or valid numbers (including decimals)
            if (value === '' || !isNaN(parseFloat(value))) {
              updateIngredient(
                groupId,
                index,
                'amount',
                value === '' ? null : parseFloat(value)
              );
            }
          }}
          placeholder=""
          className="bg-slate-700/50 border-slate-600 text-white text-sm"
        />
      </div>

      {/* Unit */}
      <div className="col-span-2">
        <label className="text-xs font-medium text-slate-300">
          Unit
        </label>
        <Select
          value={ingredient.unit}
          onChange={(e) =>
            updateIngredient(groupId, index, 'unit', e.target.value)
          }
          options={UNIT_OPTIONS}
          placeholder="Select unit"
          allowEmpty
        />
      </div>

      {/* Master Ingredient Selector */}
      <div className="col-span-6">
        <label className="text-xs font-medium text-slate-300">
          Ingredient
        </label>
        <IngredientSelector
          groupId={groupId}
          index={index}
          ingredient={ingredient}
          isOpen={openIngredientPopover === `${groupId}-${index}`}
          onOpenChange={(open) =>
            setOpenIngredientPopover(
              open ? `${groupId}-${index}` : null
            )
          }
          onSelect={(masterIngredient) => {
            selectMasterIngredient(groupId, index, masterIngredient);
            setOpenIngredientPopover(null);
          }}
        />
      </div>

      {/* Remove Button */}
      <div className="col-span-1">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => removeIngredient(groupId, index)}
          className="w-full h-8 p-1 bg-red-600/20 border-red-500/50 text-red-400 hover:bg-red-600/30 hover:border-red-500 transition-all"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}

// Helper function to convert flat ingredients to grouped format
function ingredientsToGroups(
  ingredients: Ingredient[],
  existingSections?: IngredientGroup[]
): IngredientGroup[] {
  // If we have existing sections, use them
  if (existingSections && existingSections.length > 0) {
    return existingSections;
  }

  if (ingredients.length === 0) {
    return [{ id: 'group-1', title: '', ingredients: [] }];
  }

  // For new meals or existing meals without sections, put all ingredients in a single group
  return [{ id: 'group-1', title: '', ingredients: [...ingredients] }];
}

// Helper function to convert grouped format back to flat ingredients
function groupsToIngredients(groups: IngredientGroup[]): Ingredient[] {
  const allIngredients: Ingredient[] = [];

  groups.forEach((group) => {
    allIngredients.push(...group.ingredients);
  });

  return allIngredients;
}

export function GroupedIngredientsForm({
  ingredients,
  ingredientSections,
  onChange,
  onSectionsChange,
}: GroupedIngredientsFormProps) {
  const [groups, setGroups] = useState<IngredientGroup[]>(() =>
    ingredientsToGroups(ingredients, ingredientSections)
  );
  const [openIngredientPopover, setOpenIngredientPopover] = useState<
    string | null
  >(null);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Helper to update parent with flat ingredients (defer to avoid setState during render)
  // Helper to update parent - use requestAnimationFrame to defer execution
  const updateParent = React.useCallback((newGroups: IngredientGroup[]) => {
    requestAnimationFrame(() => {
      const flatIngredients = groupsToIngredients(newGroups);
      onChange(flatIngredients);
      if (onSectionsChange) {
        onSectionsChange(newGroups);
      }
    });
  }, [onChange, onSectionsChange]);

  const updateGroupTitle = (groupId: string, title: string) => {
    setGroups((prev) => {
      const newGroups = prev.map((group) =>
        group.id === groupId ? { ...group, title } : group
      );
      updateParent(newGroups);
      return newGroups;
    });
  };

  const updateIngredient = (
    groupId: string,
    index: number,
    field: keyof Ingredient,
    value: string | number | null
  ) => {
    setGroups((prev) => {
      const newGroups = prev.map((group) => {
        if (group.id === groupId) {
          const newIngredients = [...group.ingredients];
          newIngredients[index] = { ...newIngredients[index], [field]: value };
          return { ...group, ingredients: newIngredients };
        }
        return group;
      });
      updateParent(newGroups);
      return newGroups;
    });
  };

  const selectMasterIngredient = (
    groupId: string,
    index: number,
    masterIngredient: MasterIngredient
  ) => {
    setGroups((prev) => {
      const newGroups = prev.map((group) => {
        if (group.id === groupId) {
          const newIngredients = [...group.ingredients];
          newIngredients[index] = {
            ...newIngredients[index],
            name: masterIngredient.name,
            emoji: masterIngredient.emoji || '🥗',
            ingredient_id: masterIngredient.id,
          };
          return { ...group, ingredients: newIngredients };
        }
        return group;
      });
      updateParent(newGroups);
      return newGroups;
    });
  };

  const addIngredient = (groupId: string) => {
    setGroups((prev) => {
      const newGroups = prev.map((group) =>
        group.id === groupId
          ? {
              ...group,
              ingredients: [
                ...group.ingredients,
                { name: '', amount: null, unit: '', emoji: '🥗' },
              ],
            }
          : group
      );
      updateParent(newGroups);
      return newGroups;
    });
  };

  const removeIngredient = (groupId: string, index: number) => {
    setGroups((prev) => {
      const newGroups = prev.map((group) =>
        group.id === groupId
          ? {
              ...group,
              ingredients: group.ingredients.filter((_, i) => i !== index),
            }
          : group
      );
      updateParent(newGroups);
      return newGroups;
    });
  };

  const addGroup = () => {
    const newGroupId = `group-${Date.now()}`;
    setGroups((prev) => {
      const newGroups = [
        ...prev,
        { id: newGroupId, title: '', ingredients: [] },
      ];
      updateParent(newGroups);
      return newGroups;
    });
  };

  const removeGroup = (groupId: string) => {
    setGroups((prev) => {
      const newGroups = prev.filter((group) => group.id !== groupId);
      updateParent(newGroups);
      return newGroups;
    });
  };

  // Handle drag end for reordering ingredients within groups
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    // Parse the IDs to get group and index information
    const activeId = active.id as string;
    const overId = over.id as string;
    
    console.log('Drag end - activeId:', activeId, 'overId:', overId);
    
    // More robust ID parsing - split from the last dash to handle complex group IDs
    const activeLastDash = activeId.lastIndexOf('-');
    const overLastDash = overId.lastIndexOf('-');
    
    if (activeLastDash === -1 || overLastDash === -1) {
      console.error('Invalid drag IDs:', activeId, overId);
      return;
    }
    
    const activeGroupId = activeId.substring(0, activeLastDash);
    const overGroupId = overId.substring(0, overLastDash);
    const activeIndex = parseInt(activeId.substring(activeLastDash + 1));
    const overIndex = parseInt(overId.substring(overLastDash + 1));

    console.log('Parsed drag data:', { activeGroupId, overGroupId, activeIndex, overIndex });

    // Only handle reordering within the same group for now
    if (activeGroupId === overGroupId && !isNaN(activeIndex) && !isNaN(overIndex)) {
      console.log('Executing drag reorder');
      setGroups((prev) => {
        const newGroups = prev.map((group) => {
          if (group.id === activeGroupId) {
            const newIngredients = arrayMove(group.ingredients, activeIndex, overIndex);
            console.log('Reordered ingredients:', newIngredients.map(i => i.name));
            return { ...group, ingredients: newIngredients };
          }
          return group;
        });
        // Call updateParent with the new groups
        updateParent(newGroups);
        return newGroups;
      });
    } else {
      console.log('Drag operation not executed - different groups or invalid indices');
    }
  };

  const totalIngredients = groups.reduce(
    (sum, group) => sum + group.ingredients.length,
    0
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <label className="text-base font-medium text-white">Ingredients</label>
        <span className="text-sm text-slate-400">
          {totalIngredients} ingredient{totalIngredients !== 1 ? 's' : ''}{' '}
          across {groups.length} section{groups.length !== 1 ? 's' : ''}
        </span>
      </div>

      {groups.map((group, groupIndex) => (
        <div
          key={group.id}
          className="bg-slate-800/30 border border-slate-600 rounded-lg p-4"
        >
          {/* Group Header */}
          <div className="flex items-center gap-3 mb-4">
            <GripVertical className="h-5 w-5 text-slate-500" />
            <div className="flex-1">
              <Input
                value={group.title}
                onChange={(e) => updateGroupTitle(group.id, e.target.value)}
                placeholder={`Section ${groupIndex + 1} title (e.g., "For the roasted acorn squash")`}
                className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 text-sm font-medium"
              />
            </div>
            {groups.length > 1 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeGroup(group.id)}
                className="bg-red-600/20 border-red-500/50 text-red-400 hover:bg-red-600/30 hover:border-red-500 transition-all"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Group Title Display */}
          {group.title && (
            <div className="mb-3 px-3 py-2 bg-blue-500/10 border border-blue-500/20 rounded-md">
              <h4 className="text-blue-300 font-medium text-sm">
                {group.title}
              </h4>
            </div>
          )}

          {/* Ingredients in this group */}
          <div className="space-y-3">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={group.ingredients.map((_, index) => `${group.id}-${index}`)}
                strategy={verticalListSortingStrategy}
              >
                {group.ingredients.map((ingredient, index) => (
                  <SortableIngredient
                    key={`${group.id}-ingredient-${index}-${ingredient.name || 'empty'}`}
                    ingredient={ingredient}
                    index={index}
                    groupId={group.id}
                    updateIngredient={updateIngredient}
                    removeIngredient={removeIngredient}
                    selectMasterIngredient={selectMasterIngredient}
                    openIngredientPopover={openIngredientPopover}
                    setOpenIngredientPopover={setOpenIngredientPopover}
                  />
                ))}
              </SortableContext>
            </DndContext>

            {/* Empty state for group */}
            {group.ingredients.length === 0 && (
              <div className="text-center py-6 text-slate-400 border-2 border-dashed border-slate-700 rounded-lg bg-slate-800/20">
                No ingredients in this section yet.
              </div>
            )}

            {/* Add ingredient to group button */}
            <div className="flex justify-center">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addIngredient(group.id)}
                className="flex items-center gap-2 bg-blue-600/20 border-blue-500/50 text-blue-400 hover:bg-blue-600/30 hover:border-blue-500 transition-all"
              >
                <Plus className="h-4 w-4" />
                Add Ingredient to {group.title || `Section ${groupIndex + 1}`}
              </Button>
            </div>
          </div>
        </div>
      ))}

      {/* Empty state */}
      {groups.length === 0 && (
        <div className="text-center py-8 text-slate-400 border-2 border-dashed border-slate-700 rounded-lg bg-slate-800/20">
          No ingredient sections yet. Click &quot;Add Section&quot; to get
          started.
        </div>
      )}

      {/* Add Section Button */}
      <div className="flex justify-center">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addGroup}
          className="flex items-center gap-2 bg-green-600/20 border-green-500/50 text-green-400 hover:bg-green-600/30 hover:border-green-500 transition-all"
        >
          <FolderPlus className="h-4 w-4" />
          Add Section
        </Button>
      </div>
    </div>
  );
}

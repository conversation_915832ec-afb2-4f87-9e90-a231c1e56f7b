'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search } from 'lucide-react';
import {
  UNIQUE_FOOD_EMOJIS,
  type FoodEmoji,
} from '@/lib/food-emojis';

interface EmojiPickerProps {
  value?: string;
  onChange: (emoji: string) => void;
  className?: string;
  placeholder?: string;
}

export function EmojiPicker({
  value,
  onChange,
  className = '',
  placeholder = 'Select emoji',
}: EmojiPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter emojis based on search term only
  let filteredEmojis = UNIQUE_FOOD_EMOJIS;

  if (searchTerm.trim()) {
    filteredEmojis = UNIQUE_FOOD_EMOJIS.filter(
      (emoji) =>
        emoji.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emoji.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  const selectedEmoji = UNIQUE_FOOD_EMOJIS.find((item) => item.emoji === value);

  // Auto-focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleEmojiSelect = (emoji: string) => {
    onChange(emoji);
    setIsOpen(false);
    setSearchTerm(''); // Clear search when selecting
  };

  return (
    <div className="relative">
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between px-3 py-2 
          bg-slate-700/50 border border-slate-600 rounded-md 
          text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500
          hover:bg-slate-600/50 transition-colors
          ${className}
        `}
      >
        <span className="flex items-center space-x-2">
          {value ? (
            <>
              <span className="text-lg">{value}</span>
              <span className="text-slate-300">
                {selectedEmoji?.name || 'Unknown'}
              </span>
            </>
          ) : (
            <span className="text-slate-400">{placeholder}</span>
          )}
        </span>
        <ChevronDown
          className={`h-4 w-4 text-slate-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50 max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search emojis..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-700/50 border border-slate-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-slate-400"
              />
            </div>
          </div>

          {/* Results Count */}
          {searchTerm.trim() && (
            <div className="px-3 py-1 text-xs text-slate-400 border-t border-slate-700">
              {filteredEmojis.length} emoji
              {filteredEmojis.length !== 1 ? 's' : ''} found
            </div>
          )}

          {/* Emoji Grid */}
          <div className="p-3 max-h-72 overflow-y-auto border-t border-slate-700">
            <div className="grid grid-cols-6 gap-2">
              {filteredEmojis.map((item) => (
                <button
                  key={`${item.emoji}-${item.name}`}
                  onClick={() => handleEmojiSelect(item.emoji)}
                  className={`
                    flex flex-col items-center p-2 rounded hover:bg-slate-700/50 
                    transition-colors group relative
                    ${value === item.emoji ? 'bg-blue-600/20 ring-1 ring-blue-500' : ''}
                  `}
                  title={`${item.name} (${item.category})`}
                >
                  <span className="text-lg">{item.emoji}</span>
                  <span className="text-xs text-slate-400 truncate w-full text-center group-hover:text-slate-300">
                    {item.name}
                  </span>
                </button>
              ))}
            </div>

            {filteredEmojis.length === 0 && (
              <div className="text-center py-4 text-slate-400 text-sm">
                No emojis found matching &quot;{searchTerm}&quot;
              </div>
            )}
          </div>
        </div>
      )}

      {/* Click outside handler */}
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
}

export default EmojiPicker;

'use client';

import * as React from 'react';
import { Button } from './button';

export interface MultiSelectProps {
  options: string[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  className?: string;
}

const MultiSelect = React.forwardRef<HTMLDivElement, MultiSelectProps>(
  (
    {
      options,
      value,
      onChange,
      placeholder = 'Select options...',
      className = '',
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const containerRef = React.useRef<HTMLDivElement>(null);

    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          containerRef.current &&
          !containerRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const toggleOption = (option: string) => {
      if (value.includes(option)) {
        onChange(value.filter((v) => v !== option));
      } else {
        onChange([...value, option]);
      }
    };

    const removeOption = (option: string) => {
      onChange(value.filter((v) => v !== option));
    };

    return (
      <div className={`relative ${className}`} ref={containerRef}>
        <div
          className="flex min-h-10 w-full rounded-md border border-slate-600 bg-slate-700/50 px-3 py-2 text-sm text-white ring-offset-slate-900 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 cursor-pointer hover:bg-slate-700 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex flex-wrap gap-1 flex-1">
            {value.length === 0 ? (
              <span className="text-slate-400">{placeholder}</span>
            ) : (
              value.map((option) => (
                <span
                  key={option}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-600/20 text-blue-400 text-xs rounded border border-blue-500/30 hover:bg-blue-600/30 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeOption(option);
                  }}
                >
                  {option}
                  <span className="text-blue-400/60 hover:text-blue-400 cursor-pointer">
                    ×
                  </span>
                </span>
              ))
            )}
          </div>
          <span className="ml-2 text-slate-400">▼</span>
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-slate-800 border border-slate-700 rounded-md shadow-lg max-h-60 overflow-auto">
            {options.map((option) => (
              <div
                key={option}
                className={`px-3 py-2 text-sm cursor-pointer text-slate-300 hover:bg-slate-700 hover:text-white transition-colors ${
                  value.includes(option) ? 'bg-slate-700 text-white' : ''
                }`}
                onClick={() => toggleOption(option)}
              >
                <span className="mr-2 text-blue-400">
                  {value.includes(option) ? '✓' : ''}
                </span>
                {option}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }
);
MultiSelect.displayName = 'MultiSelect';

export { MultiSelect };

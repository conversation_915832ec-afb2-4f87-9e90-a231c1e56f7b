'use client';

import { Suspense, useEffect } from 'react';
import { Auth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';
import { supabase } from '@/lib/supabase';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';

function AuthFormContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user, isAdmin } = useAuth();
  const error = searchParams.get('error');

  // Redirect after successful sign-in
  useEffect(() => {
    if (user && isAdmin) {
      console.log('Auth form: User signed in, redirecting to meals');
      router.replace('/meals');
    } else if (user && !isAdmin) {
      console.log('Auth form: Non-admin user, showing access denied');
      router.replace('/auth?error=access_denied');
    }
  }, [user, isAdmin, router]);

  return (
    <div className="min-h-screen bg-gradient-admin flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="card-dark p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white mb-2">
              MenuMaker Admin
            </h1>
            <p className="text-slate-400">
              Sign in to access the admin dashboard
            </p>

            {error === 'access_denied' && (
              <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">
                  Access denied. Only admin users can access this dashboard.
                </p>
              </div>
            )}
          </div>

          <Auth
            supabaseClient={supabase}
            appearance={{
              theme: ThemeSupa,
              variables: {
                default: {
                  colors: {
                    brand: '#3b82f6',
                    brandAccent: '#2563eb',
                    brandButtonText: 'white',
                    defaultButtonBackground: '#1e293b',
                    defaultButtonBackgroundHover: '#334155',
                    defaultButtonBorder: '#475569',
                    defaultButtonText: '#f1f5f9',
                    dividerBackground: '#475569',
                    inputBackground: '#1e293b',
                    inputBorder: '#475569',
                    inputBorderHover: '#64748b',
                    inputBorderFocus: '#3b82f6',
                    inputText: '#f1f5f9',
                    inputLabelText: '#cbd5e1',
                    inputPlaceholder: '#94a3b8',
                    messageText: '#f1f5f9',
                    messageTextDanger: '#ef4444',
                    anchorTextColor: '#60a5fa',
                    anchorTextHoverColor: '#93c5fd',
                  },
                  space: {
                    spaceSmall: '4px',
                    spaceMedium: '8px',
                    spaceLarge: '16px',
                    labelBottomMargin: '8px',
                    anchorBottomMargin: '4px',
                    emailInputSpacing: '4px',
                    socialAuthSpacing: '4px',
                    buttonPadding: '10px 15px',
                    inputPadding: '10px 15px',
                  },
                  fontSizes: {
                    baseBodySize: '14px',
                    baseInputSize: '14px',
                    baseLabelSize: '14px',
                    baseButtonSize: '14px',
                  },
                  fonts: {
                    bodyFontFamily: `ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif`,
                    buttonFontFamily: `ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif`,
                    inputFontFamily: `ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif`,
                    labelFontFamily: `ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif`,
                  },
                  borderWidths: {
                    buttonBorderWidth: '1px',
                    inputBorderWidth: '1px',
                  },
                  radii: {
                    borderRadiusButton: '6px',
                    buttonBorderRadius: '6px',
                    inputBorderRadius: '6px',
                  },
                },
              },
              className: {
                container: 'auth-container',
                button: 'auth-button',
                input: 'auth-input',
                label: 'auth-label',
                message: 'auth-message',
              },
            }}
            theme="dark"
            providers={[]}
            onlyThirdPartyProviders={false}
            magicLink={false}
            showLinks={false}
            view="sign_in"
            localization={{
              variables: {
                sign_in: {
                  email_label: 'Email address',
                  password_label: 'Password',
                  button_label: 'Sign In',
                  loading_button_label: 'Signing In...',
                },
              },
            }}
          />
        </div>
      </div>
    </div>
  );
}

export function AuthForm() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gradient-admin flex items-center justify-center">
          <div className="card-dark p-8">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-blue-600 rounded-full animate-pulse"></div>
              <span className="text-white">Loading...</span>
            </div>
          </div>
        </div>
      }
    >
      <AuthFormContent />
    </Suspense>
  );
}

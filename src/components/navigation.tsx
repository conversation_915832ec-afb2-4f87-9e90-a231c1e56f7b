'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuth } from '@/lib/auth';
import {
  UtensilsCrossed,
  Sparkles,
  Settings,
  LogOut,
  Carrot,
} from 'lucide-react';

const navigation = [
  { name: 'Meals', href: '/meals', icon: UtensilsCrossed },
  { name: 'Ingredients', href: '/ingredients', icon: Carrot },
  { name: 'AI Meals', href: '/ai-meals', icon: Sparkles },
];

export function Navigation() {
  const pathname = usePathname();

  return (
    <nav className="flex space-x-8">
      {navigation.map((item) => {
        const Icon = item.icon;
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary',
              pathname === item.href ? 'text-primary' : 'text-muted-foreground'
            )}
          >
            <Icon className="h-4 w-4" />
            <span>{item.name}</span>
          </Link>
        );
      })}
    </nav>
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const { user, isAdmin, signOut } = useAuth();

  return (
    <div className="sidebar-dark flex h-full w-64 flex-col shadow-xl relative">
      <div className="flex flex-col flex-1 pt-6 pb-4 overflow-y-auto">
        {/* Logo/Brand Section */}
        <div className="flex items-center flex-shrink-0 px-6 mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-slate-800 rounded-sm"></div>
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">MenuMaker Admin</h1>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 space-y-1">
          {navigation.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                  isActive
                    ? 'bg-slate-800 text-white border border-slate-600/50'
                    : 'text-slate-400 hover:bg-slate-800/50 hover:text-white'
                )}
              >
                <Icon
                  className={cn(
                    'mr-3 h-4 w-4 transition-colors duration-200',
                    isActive
                      ? 'text-white'
                      : 'text-slate-500 group-hover:text-slate-300'
                  )}
                />
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>

        {/* Bottom section */}
        <div className="mt-auto px-4 py-4 border-t border-slate-700/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center">
                <Settings className="h-4 w-4 text-slate-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-white">
                  {user?.email?.split('@')[0] || 'Admin User'}
                </p>
                <p className="text-xs text-slate-400">
                  {isAdmin ? 'Admin User' : 'Regular User'}
                </p>
              </div>
            </div>
            <button
              onClick={signOut}
              className="p-2 text-slate-400 hover:text-white hover:bg-slate-800 rounded-lg transition-colors"
              title="Sign Out"
            >
              <LogOut className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

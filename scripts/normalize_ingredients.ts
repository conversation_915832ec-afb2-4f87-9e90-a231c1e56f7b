// Script to extract and normalize ingredients from meals

interface IngredientData {
  name: string;
  emoji?: string;
  count: number;
}

// Function to normalize ingredient names
function normalizeIngredientName(name: string): string {
  if (!name) return '';

  // Convert to lowercase
  let normalized = name.toLowerCase().trim();

  // Remove parenthetical content (e.g., "(<PERSON><PERSON><PERSON> is best)", "(optional)", "(or avocado oil)")
  normalized = normalized.replace(/\s*\([^)]*\)/g, '');

  // Remove text after "or" in alternatives (e.g., "butter or oil" -> "butter")
  normalized = normalized.replace(/\s+or\s+.*/i, '');

  // Remove common suffixes like "to taste", "optional", etc.
  normalized = normalized.replace(
    /\s*(to taste|optional|if desired|as needed|for serving|for topping|for garnish)$/i,
    ''
  );

  // Remove leading/trailing commas and spaces
  normalized = normalized.replace(/^[,\s]+|[,\s]+$/g, '');

  // Remove quantity-related words that might be in the name
  normalized = normalized.replace(
    /^\d+(\.\d+)?\s*(cups?|tbs?p?|tsps?|lbs?|oz|ounces?|pounds?|quarts?|pints?|gallons?|liters?|ml|grams?|kg)\s+/i,
    ''
  );

  // Handle special cases
  const specialCases: { [key: string]: string } = {
    'garlic, minced': 'garlic',
    'garlic minced': 'garlic',
    'minced garlic': 'garlic',
    'garlic, pressed': 'garlic',
    'garlic pressed': 'garlic',
    'pressed garlic': 'garlic',
    'garlic, finely minced': 'garlic',
    'garlic, chopped': 'garlic',
    'chopped garlic': 'garlic',
    'cloves garlic': 'garlic',
    'cloves, minced': 'garlic',
    'salt and pepper': 'salt',
    'kosher salt and black pepper': 'kosher salt',
    'butter, melted': 'butter',
    'melted butter': 'butter',
    'onion, diced': 'onion',
    'onion, chopped': 'onion',
    'onions, sliced': 'onion',
    'bell pepper, diced': 'bell pepper',
    leaves: 'bay leaves',
    leaf: 'bay leaf',
    buns: 'hamburger buns',
    breads: 'bread',
    'dried oregano': 'oregano',
    'dried basil': 'basil',
    'dried thyme': 'thyme',
    'ground cumin': 'cumin',
    'ground black pepper': 'black pepper',
    'freshly grated parmesan cheese': 'parmesan cheese',
    'grated parmesan cheese': 'parmesan cheese',
    'shredded parmesan cheese': 'parmesan cheese',
    'shredded cheddar cheese': 'cheddar cheese',
    'shredded mozzarella cheese': 'mozzarella cheese',
    'all-purpose flour': 'flour',
    'granulated sugar': 'sugar',
    'brown sugar': 'brown sugar',
    'lean ground beef': 'ground beef',
    'boneless, skinless chicken breasts': 'chicken breasts',
    'boneless, skinless chicken thighs': 'chicken thighs',
    'chicken broth': 'chicken broth',
    'beef broth': 'beef broth',
    'vegetable broth': 'vegetable broth',
    'low-sodium chicken broth': 'chicken broth',
    'olive oil': 'olive oil',
    'vegetable oil': 'vegetable oil',
    'sesame oil': 'sesame oil',
    'red wine vinegar': 'red wine vinegar',
    'rice vinegar': 'rice vinegar',
    'apple cider vinegar': 'apple cider vinegar',
    'balsamic vinegar': 'balsamic vinegar',
    'fresh lemon juice': 'lemon juice',
    'fresh lime juice': 'lime juice',
    'lemon juice': 'lemon juice',
    'lime juice': 'lime juice',
    'worcestershire sauce': 'worcestershire sauce',
    'soy sauce': 'soy sauce',
    'dijon mustard': 'dijon mustard',
    mayonnaise: 'mayonnaise',
    'sour cream': 'sour cream',
    'heavy cream': 'heavy cream',
    'cream cheese': 'cream cheese',
    eggs: 'eggs',
    egg: 'egg',
    milk: 'milk',
    honey: 'honey',
    ketchup: 'ketchup',
    salsa: 'salsa',
    'tomato sauce': 'tomato sauce',
    'tomato paste': 'tomato paste',
    'italian seasoning': 'italian seasoning',
    'taco seasoning': 'taco seasoning',
    'chili powder': 'chili powder',
    paprika: 'paprika',
    'smoked paprika': 'smoked paprika',
    'garlic powder': 'garlic powder',
    'onion powder': 'onion powder',
    'garlic salt': 'garlic salt',
    'black pepper': 'black pepper',
    pepper: 'black pepper',
    'cayenne pepper': 'cayenne pepper',
    'red pepper flakes': 'red pepper flakes',
    'crushed red pepper flakes': 'red pepper flakes',
    'kosher salt': 'kosher salt',
    salt: 'salt',
    'baking soda': 'baking soda',
    'baking powder': 'baking powder',
    cornstarch: 'cornstarch',
    flour: 'flour',
    sugar: 'sugar',
    water: 'water',
    cilantro: 'cilantro',
    'chopped cilantro': 'cilantro',
    parsley: 'parsley',
    'sesame seeds': 'sesame seeds',
    'crumbled feta cheese': 'feta cheese',
    tortillas: 'tortillas',
    'red onion': 'red onion',
  };

  // Check if the normalized name matches any special case
  if (specialCases[normalized]) {
    return specialCases[normalized];
  }

  // Clean up any remaining issues
  normalized = normalized.replace(/\s+/g, ' ').trim();

  return normalized;
}

// Export for use in other scripts
export { normalizeIngredientName, type IngredientData };

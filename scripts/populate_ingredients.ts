// Script to populate the ingredients table with normalized ingredient names
import { createClient } from '@supabase/supabase-js';
import { normalizeIngredientName } from './normalize_ingredients';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function populateIngredients() {
  console.log('Starting ingredient population...');

  try {
    // Fetch all meals with ingredients
    console.log('Fetching all meals...');
    const { data: meals, error: mealsError } = await supabase
      .from('meals')
      .select('id, name, ingredients')
      .not('ingredients', 'is', null);

    if (mealsError) {
      console.error('Error fetching meals:', mealsError);
      return;
    }

    console.log(`Found ${meals?.length || 0} meals with ingredients`);

    // Extract and normalize all ingredients
    const ingredientMap = new Map<string, { emoji?: string; count: number }>();

    meals?.forEach((meal) => {
      if (meal.ingredients && Array.isArray(meal.ingredients)) {
        meal.ingredients.forEach((ingredient: any) => {
          if (ingredient.name) {
            const normalized = normalizeIngredientName(ingredient.name);
            if (normalized) {
              const existing = ingredientMap.get(normalized) || { count: 0 };
              ingredientMap.set(normalized, {
                emoji: ingredient.emoji || existing.emoji,
                count: existing.count + 1,
              });
            }
          }
        });
      }
    });

    console.log(`Found ${ingredientMap.size} unique normalized ingredients`);

    // Prepare ingredients for insertion
    const ingredientsToInsert = Array.from(ingredientMap.entries()).map(
      ([name, data]) => ({
        name,
        emoji: data.emoji || null,
        category: null, // We can add categories later
      })
    );

    // Insert ingredients in batches to avoid conflicts
    const batchSize = 100;
    let inserted = 0;

    for (let i = 0; i < ingredientsToInsert.length; i += batchSize) {
      const batch = ingredientsToInsert.slice(i, i + batchSize);

      const { data, error } = await supabase
        .from('ingredients')
        .upsert(batch, { onConflict: 'name' })
        .select();

      if (error) {
        console.error(`Error inserting batch ${i / batchSize + 1}:`, error);
      } else {
        inserted += data?.length || 0;
        console.log(
          `Inserted batch ${i / batchSize + 1} (${data?.length} ingredients)`
        );
      }
    }

    console.log(`Successfully inserted/updated ${inserted} ingredients`);

    // Log some statistics
    const topIngredients = Array.from(ingredientMap.entries())
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 20);

    console.log('\nTop 20 most common ingredients:');
    topIngredients.forEach(([name, data], index) => {
      console.log(`${index + 1}. ${name} (${data.count} uses)`);
    });
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the script if called directly
if (require.main === module) {
  populateIngredients();
}

export { populateIngredients };
